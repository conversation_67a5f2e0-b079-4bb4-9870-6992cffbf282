import json
import logging
import os

import requests

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def is_sns_message(message):
    return 'Message' in message

def record_to_message(record):
    if 'Message' in record:
        return json.loads(record['Message'])
    elif 'body' in record:
        base_msg = json.loads(record['body'])
        if is_sns_message(base_msg):
            return json.loads(base_msg['Message'])
        else:
            return base_msg
    else:
        logger.error('Unknown record: %s', record)
        return None

def is_pipeline_failure_message(message):
    return 'detail' in message and message['detail'].get('state', '') == 'FAILED'

def is_planned_build_message(message):
    return 'planned_builds' in message

def is_completed_deploy_message(message):
    return 'completed_deploy' in message

def lambda_handler(event, _):
    # Testing a change, again
    try:
        for record in event['Records']:
            msg = record_to_message(record)
            text_to_send = ''
            if is_pipeline_failure_message(msg):
                pipeline_name = msg['detail']['pipeline']
                stage = msg['detail']['stage']
                detail = f'Pipeline {pipeline_name} failed on stage {stage}!'
                link = f'https://eu-west-2.console.aws.amazon.com/codesuite/codepipeline/pipelines/{pipeline_name}/view?region=eu-west-2'
                text_to_send = f"{detail} - <{link}|Link>"
            elif is_planned_build_message(msg):
                text_to_send = f'Planned build(s): {msg["planned_builds"]}'
            elif is_completed_deploy_message(msg):
                text_to_send = f'Completed deployment(s): {msg["completed_deploy"]}'
            if text_to_send:
                slack_channel = os.environ['SLACK_WEBHOOK']
                requests.post(slack_channel, timeout=15, json={
                    "text": text_to_send
                })
    except (ValueError, KeyError) as e:
        logger.error(event, exc_info=e)
