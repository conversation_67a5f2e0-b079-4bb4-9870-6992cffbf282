"""
Django settings for cdr_db project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-6b=1^cnb(@6$1%*g6hxtr45%ooin)_q5rwa35s@a(qgvrkcqav'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'core',
    'provider'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'cdr_db.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'cdr_db.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "cdr_db",
        "HOST": os.environ.get('DB_HOST', 'localhost'),
        "USER": os.environ.get('DB_USER', 'cdr_db'),
        "PASSWORD": os.environ.get('DB_PASSWORD'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = 'static'

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

LAMBDA_API_TOKEN = os.environ.get('LAMBDA_API_TOKEN')
NEXUS_API_TOKEN = os.environ.get('NEXUS_API_TOKEN')

TRANSATEL_ACCESS_TOKEN = os.environ.get('TRANSATEL_ACCESS_TOKEN')

GAMMA_URL = os.environ.get('GAMMA_URL')
GAMMA_USERNAME = os.environ.get('GAMMA_USERNAME')
GAMMA_PASSWORD = os.environ.get('GAMMA_PASSWORD')
GAMMA_CHANNEL_PARTNER_ID = os.environ.get('GAMMA_CHANNEL_PARTNER_ID')
GAMMA_USER_ID = os.environ.get('GAMMA_USER_ID')

GAMMA_WEBHOOK_SECRET_KEY = os.environ.get('GAMMA_WEBHOOK_SECRET_KEY')

AGNOSTIC_CDR_GENERATED_TOPIC_ARN = os.environ.get('AGNOSTIC_CDR_GENERATED_TOPIC_ARN')
PROVIDER_SPECIFIC_CDR_TOPIC_ARN = os.environ.get('PROVIDER_SPECIFIC_CDR_TOPIC_ARN')

try:
    from .local_settings import *  # pylint: disable=wildcard-import, unused-wildcard-import
except ImportError:
    pass
