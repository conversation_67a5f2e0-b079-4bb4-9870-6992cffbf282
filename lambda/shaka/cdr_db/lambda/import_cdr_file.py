# pylint: disable=duplicate-code
# pylint: disable=wrong-import-position
import gzip
import json
import logging
import os
import shutil
import tempfile
from pathlib import Path

import boto3
import botocore.config
import django

django.setup()

# Now this script or any imported module can use any part of Django it needs.
from django.db import transaction

from core.models import FileImport

from .cdr_csv_importer import IMPORTERS

logger = logging.getLogger()
logger.setLevel(logging.INFO)


class StrangeFileError(RuntimeError):
    pass


def key_to_cdr_filename(s3_key):
    return s3_key.split('/')[-1]

def filename_to_cdr_type(filename):
    return filename.split('_')[1]

def insert_cdr_objects(file_on_disk, cdr_type):
    importer = IMPORTERS[cdr_type](file_on_disk)
    objects = importer.convert_to_unsaved_objects()
    importer.model_class.objects.bulk_create(objects)

def is_strange_file(sample_line):
    return len(sample_line.split(';;;;')) > 3

def check_for_strange_file(file_on_disk):
    with open(file_on_disk, 'r', encoding='iso-8859-1') as f:
        first_line = next(f)
        logger.info('Checking for strange %s, %s', first_line, is_strange_file(first_line))
        if is_strange_file(first_line):
            raise StrangeFileError()


def lambda_handler(sqs_event, __):
    bucket_name = os.environ['S3_BUCKET_NAME']
    s3 = boto3.resource('s3', config=botocore.config.Config(s3={'addressing_style':'path'}))
    bucket = s3.Bucket(bucket_name)
    files_completed = []

    with tempfile.TemporaryDirectory() as tempdir:
        for record in sqs_event['Records']:
            event_records = json.loads(record['body'])
            for event in event_records['Records']:
                if event['s3']['bucket']['name'] != bucket_name:
                    raise RuntimeError('Invalid bucket', event)
                key = event['s3']['object']['key']
                potentially_completed_file = process_key(key, bucket, tempdir)
                if potentially_completed_file:
                    files_completed.append(potentially_completed_file)
    if files_completed:
        logger.info('Imported %s', files_completed)
    else:
        logger.info('No files to import')


def process_key(key, bucket, tempdir):
    def download_s3_file(key):
        gzip_filename = key_to_cdr_filename(key)
        csv_filename = gzip_filename.replace('.gz', '')
        gzip_fpath = Path(tempdir) / gzip_filename
        csv_fpath = Path(tempdir) / csv_filename
        bucket.download_file(key, gzip_fpath)
        with gzip.open(gzip_fpath, 'rb') as f_in, open(csv_fpath, 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)
        return csv_fpath

    def process_cdr_file(key):
        logger.info('Downloading %s', key)
        file_on_disk = download_s3_file(key)
        cdr_type = filename_to_cdr_type(key_to_cdr_filename(key))
        check_for_strange_file(file_on_disk)
        insert_cdr_objects(file_on_disk, cdr_type)

    filename = key_to_cdr_filename(key)
    with transaction.atomic():
        file_import = FileImport.objects.select_for_update().filter(filename=filename).first()
        if file_import is None:
            file_import = FileImport.objects.create(filename=filename, status=FileImport.Status.IN_PROGRESS)
            logger.info('Importing cdr file %s', key)
        elif file_import.status == FileImport.Status.SKIPPED_STRANGE:
            logger.info('This file is detected as skipped (rated) %s', key)
            return None
        elif file_import.status == FileImport.Status.IN_PROGRESS:
            logger.info('Another lambda is currently processing %s', key)
            return None
        elif file_import.status == FileImport.Status.ERRORED:
            logger.info('Trying %s again because it errored previously', key)
        else:
            logger.info('Another lambda has finished processing %s with status %s', key, file_import.status)
            return None
    try:
        process_cdr_file(key)
        file_import.status = FileImport.Status.DONE
        file_import.save()
        logger.info('Completed %s', key)
        return filename
    except StrangeFileError:
        file_import.status = FileImport.Status.SKIPPED_STRANGE
        file_import.save()
        logger.warning('Found strange file %s', key, exc_info=True)
        return None
    except:
        file_import.status = FileImport.Status.ERRORED
        file_import.save()
        logger.error('Errored %s', key, exc_info=True)
        raise
    return None
