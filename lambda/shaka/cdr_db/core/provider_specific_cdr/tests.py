import datetime
import unittest
from unittest import mock

import attrs
import time_machine

from core.provider_specific_cdr import converters, topics
from core.provider_specific_cdr import events as provider_specific_cdr_events
from core.agnostic_cdr import events as agnostic_cdr_events
from core.agnostic_cdr import topics as agnostic_topics


class TestProviderToAgnosticConverter(unittest.TestCase):
    def test_can_convert_field_via_name_only(self):
        @attrs.frozen
        class _TestProviderEvent:
            test_field: int

        @attrs.frozen
        class _TestAgnosticEvent:
            test_field: int

        test_converter = converters.ProviderToAgnosticConverter(
            fields=(
                "test_field",
            ),
            agnostic_cdr_type=_TestAgnosticEvent
        )

        provider_event = _TestProviderEvent(test_field=1)
        agnostic_event = test_converter.convert(provider_event)
        self.assertEqual(agnostic_event.test_field, 1)

    def test_can_convert_field_via_custom_field(self):
        @attrs.frozen
        class _TestProviderEvent:
            test_field: int

        @attrs.frozen
        class _TestAgnosticEvent:
            some_other_field: str

        test_converter = converters.ProviderToAgnosticConverter(
            fields=(
                converters.Field("test_field", "some_other_field", str),
            ),
            agnostic_cdr_type=_TestAgnosticEvent
        )

        provider_event = _TestProviderEvent(test_field=1)
        agnostic_event = test_converter.convert(provider_event)
        self.assertEqual(agnostic_event.some_other_field, "1")

    def test_missing_field_on_provider_event_causes_error(self):
        @attrs.frozen
        class _TestProviderEvent:
            test_field: str

        @attrs.frozen
        class _TestAgnosticEvent:
            some_other_field: str

        test_converter = converters.ProviderToAgnosticConverter(
            fields=(
                "non_existent_field",
            ),
            agnostic_cdr_type=_TestAgnosticEvent
        )

        provider_event = _TestProviderEvent(test_field="test")
        with self.assertRaises(AttributeError):
            test_converter.convert(provider_event)

    def test_missing_field_on_agnostic_event_causes_error(self):
        @attrs.frozen
        class _TestProviderEvent:
            test_field: str

        @attrs.frozen
        class _TestAgnosticEvent:
            some_other_field: str

        test_converter = converters.ProviderToAgnosticConverter(
            fields=(
                "test_field",
            ),
            agnostic_cdr_type=_TestAgnosticEvent
        )

        provider_event = _TestProviderEvent(test_field="test")
        with self.assertRaises(TypeError):
            test_converter.convert(provider_event)


nullable_gamma_field_we_dont_care_about = [
    "call_type", "call_cause_definition_required", "customer_identifier", "non_charged_party", "call_date",
    "call_time", "duration", "bytes_transmitted", "bytes_received", "description", "chargecode", "time_band",
    "salesprice", "salesprice_pre_bundle", "extension", "ddi", "grouping_id", "call_class_feature", "carrier",
    "recording", "vat", "country_of_origin", "network", "retail_tariff_code", "remote_network", "apn",
    "diverted_number", "ring_time", "record_id", "currency", "caller_line_identity", "network_access_reference",
    "ngcs_access_charge", "ngcs_service_charge", "total_bytes_transferred", "user_id", "onward_billing_reference",
    "contract_name", "bundle_name", "bundle_allowance", "discount_reference", "routing_code",
]


class TestGammaAgnosticConverter(unittest.TestCase):
    def test_converts_data_cdr(self):
        provider_event = provider_specific_cdr_events.GammaDataCDREvent(
            msisdn="1234",
            event_time=datetime.datetime(2025, 1, 1),
            data_usage_bytes=1024,
            roaming_zone="A",
            filename="filename",
            **{field: None for field in nullable_gamma_field_we_dont_care_about}
        )
        agnostic_cdr = converters.gamma_data_agnostic_converter.convert(provider_event)
        self.assertEqual(agnostic_cdr, agnostic_cdr_events.AgnosticDataCDREvent(
            msisdn="1234",
            event_time=datetime.datetime(2025, 1, 1),
            data_usage_bytes=1024,
            roaming_zone="A"
        ))

    def test_converts_voice_cdr(self):
        provider_event = provider_specific_cdr_events.GammaVoiceCDREvent(
            msisdn="1234",
            event_time=datetime.datetime(2025, 1, 1),
            duration_seconds=10,
            filename="filename",
            **{field: None for field in nullable_gamma_field_we_dont_care_about}
        )
        agnostic_cdr = converters.gamma_voice_agnostic_converter.convert(provider_event)
        self.assertEqual(agnostic_cdr, agnostic_cdr_events.AgnosticVoiceCDREvent(
            msisdn="1234",
            event_time=datetime.datetime(2025, 1, 1),
            duration_seconds=10,
        ))

    def test_converts_sms_cdr(self):
        provider_event = provider_specific_cdr_events.GammaSMSCDREvent(
            msisdn="1234",
            event_time=datetime.datetime(2025, 1, 1),
            quantity=1,
            filename="filename",
            **{field: None for field in nullable_gamma_field_we_dont_care_about}
        )
        agnostic_cdr = converters.gamma_sms_agnostic_converter.convert(provider_event)
        self.assertEqual(agnostic_cdr, agnostic_cdr_events.AgnosticSMSCDREvent(
            msisdn="1234",
            event_time=datetime.datetime(2025, 1, 1),
            quantity=1,
        ))


class TestProviderCDRReceivedTopic(unittest.TestCase):
    @mock.patch.object(agnostic_topics.agnostic_cdr_generated.publisher, "publish")
    def test_can_handle_gamma_data_event(self, mock_publish):
        test_event = {
            "filename": "some_filename",
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "data_usage_bytes": 1024,
            "roaming_zone": "A",
            "__type": provider_specific_cdr_events.GammaDataCDREvent.__name__,
            "__published_at": **********.0,
            **{field: None for field in nullable_gamma_field_we_dont_care_about}
        }

        with time_machine.travel(datetime.datetime(2025, 1, 1)):
            topics.provider_specific_cdr_received.handle(test_event)

        mock_publish.assert_called_once_with(
            {
                'msisdn': '1234',
                'event_time': '2025-01-01T00:00:00',
                'data_usage_bytes': 1024,
                'roaming_zone': 'A',
                '__type': 'AgnosticDataCDREvent',
                '__published_at': **********.0
            }
        )

    @mock.patch.object(agnostic_topics.agnostic_cdr_generated.publisher, "publish")
    def test_can_handle_gamma_voice_event(self, mock_publish):
        test_event = {
            "filename": "some_filename",
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "duration_seconds": 60,
            "__type": provider_specific_cdr_events.GammaVoiceCDREvent.__name__,
            "__published_at": **********.0,
            **{field: None for field in nullable_gamma_field_we_dont_care_about}
        }

        with time_machine.travel(datetime.datetime(2025, 1, 1)):
            topics.provider_specific_cdr_received.handle(test_event)

        mock_publish.assert_called_once_with(
            {
                'msisdn': '1234',
                'event_time': '2025-01-01T00:00:00',
                'duration_seconds': 60,
                '__type': 'AgnosticVoiceCDREvent',
                '__published_at': **********.0
            }
        )

    @mock.patch.object(agnostic_topics.agnostic_cdr_generated.publisher, "publish")
    def test_can_handle_gamma_sms_event(self, mock_publish):
        test_event = {
            "filename": "some_filename",
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "quantity": 1,
            "__type": provider_specific_cdr_events.GammaSMSCDREvent.__name__,
            "__published_at": **********.0,
            **{field: None for field in nullable_gamma_field_we_dont_care_about}
        }

        with time_machine.travel(datetime.datetime(2025, 1, 1)):
            topics.provider_specific_cdr_received.handle(test_event)

        mock_publish.assert_called_once_with(
            {
                'msisdn': '1234',
                'event_time': '2025-01-01T00:00:00',
                'quantity': 1,
                '__type': 'AgnosticSMSCDREvent',
                '__published_at': **********.0
            }
        )
