from datetime import datetime

import pytz
from django.db import models


def parse_timezone_dt(value):
    timezone = 'Europe/London'
    return pytz.timezone(timezone).localize(
        datetime.strptime(value, "%Y%m%d%H%M%S")
    )


# Create your models here.
class MVNO(models.Model):
    id = models.CharField(max_length=100, primary_key=True)


class VoiceCDRQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        return self.filter(disconnect_time__gte=start_date, disconnect_time__lt=end_date)

    def sum_usage(self):
        return self.aggregate(total_usage=models.Sum('duration'))['total_usage']

    def annotate_usage(self):
        return self.annotate(total_usage=models.Sum('duration'))


class VoiceCDR(models.Model):
    time_field = 'disconnect_time'
    objects = models.Manager.from_queryset(VoiceCDRQuerySet)()

    class CallType(models.TextChoices):
        MOC = 'MOC', 'MOC'
        MTC = 'MTC', 'MTC'
        MFC = 'MFC', 'MFC'
        ROC = 'ROC', 'ROC'
        RTC = 'RTC', 'RTC'
        RFC = 'RFC', 'RFC'

    record_id = models.BigIntegerField(db_index=True, unique=True)
    msisdn = models.CharField(max_length=30, db_index=True)
    imsi = models.CharField(max_length=30, db_index=True)
    subscriber_id = models.CharField(max_length=100, db_index=True)
    cos = models.CharField(max_length=100)
    mvno = models.ForeignKey(MVNO, on_delete=models.CASCADE)
    call_type = models.CharField(max_length=20, choices=CallType.choices)
    bearer = models.CharField(max_length=100, blank=True, null=False, default='')
    anumber = models.CharField(max_length=100, db_index=True)
    bnumber = models.CharField(max_length=100)
    rnumber = models.CharField(max_length=100, blank=True, null=False, default='')
    prefix = models.CharField(max_length=100)
    destination = models.CharField(max_length=100)
    location_zone = models.CharField(max_length=100)
    location_code = models.CharField(max_length=100)
    country_code = models.CharField(max_length=3, db_index=True)
    mcc = models.CharField(blank=True, null=True, max_length=10)
    mnc = models.CharField(blank=True, null=True, max_length=10)
    cell_id = models.CharField(max_length=100, blank=True, null=False, default='')
    setup_time = models.DateTimeField(db_index=True)
    answer_time = models.DateTimeField(db_index=True)
    disconnect_time = models.DateTimeField(db_index=True)
    duration = models.IntegerField()
    filename = models.CharField(max_length=100)

    class Meta:
        ordering = ['-record_id']
        indexes = [
            models.Index(fields=['msisdn', 'setup_time']),
            models.Index(fields=['msisdn', 'answer_time']),
            models.Index(fields=['msisdn', 'disconnect_time']),
        ]


class DataCDRQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        return self.filter(event_time__gte=start_date, event_time__lt=end_date)

    def sum_usage(self):
        return self.aggregate(total_usage=models.Sum('usu'))['total_usage']

    def group_by_msisdn(self):
        return self.values('msisdn')

    def annotate_usage(self):
        return self.annotate(total_usage=models.Sum('usu'))


class DataCDR(models.Model):
    time_field = 'event_time'
    objects = models.Manager.from_queryset(DataCDRQuerySet)()

    class CallType(models.TextChoices):
        MOG = 'MOG', 'MOG'
        ROG = 'ROG', 'ROG'

    record_id = models.BigIntegerField(db_index=True, unique=True)
    msisdn = models.CharField(max_length=30, db_index=True)
    imsi = models.CharField(max_length=30, db_index=True)
    subscriber_id = models.CharField(max_length=100, db_index=True)
    cos = models.CharField(max_length=100)
    mvno = models.ForeignKey(MVNO, on_delete=models.CASCADE)
    call_type = models.CharField(max_length=20, choices=CallType.choices)
    apn = models.CharField(max_length=100)
    location_zone = models.CharField(max_length=100)
    location_code = models.CharField(max_length=100)
    country_code = models.CharField(max_length=3, db_index=True)
    mcc = models.CharField(blank=True, null=True, max_length=10)
    mnc = models.CharField(blank=True, null=True, max_length=10)
    cell_id = models.CharField(max_length=100, blank=True, null=False, default='')
    rat = models.CharField(max_length=100, blank=True, null=False, default='')
    imei = models.CharField(max_length=100, blank=True, null=False, default='')
    event_time = models.DateTimeField(db_index=True)
    usu = models.BigIntegerField()
    filename = models.CharField(max_length=100)

    def __str__(self):
        return f"DataCDR {self.record_id}, {self.msisdn}, {self.event_time}, {self.usu}"

    class Meta:
        ordering = ['-record_id']
        indexes = [
            models.Index(fields=['msisdn', 'event_time'])
        ]


class SMSCDRQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        return self.filter(event_time__gte=start_date, event_time__lt=end_date)

    def sum_usage(self):
        return self.count()

    def annotate_usage(self):
        return self.annotate(total_usage=models.Count('id'))


class SMSCDR(models.Model):
    time_field = 'event_time'
    objects = models.Manager.from_queryset(SMSCDRQuerySet)()

    class CallType(models.TextChoices):
        MOS = 'MOS', 'MOS'
        ROS = 'ROS', 'ROS'

    record_id = models.BigIntegerField(db_index=True, unique=True)
    msisdn = models.CharField(max_length=30, db_index=True)
    imsi = models.CharField(max_length=30, db_index=True)
    subscriber_id = models.CharField(max_length=100, db_index=True)
    cos = models.CharField(max_length=100)
    mvno = models.ForeignKey(MVNO, on_delete=models.CASCADE)
    call_type = models.CharField(max_length=20, choices=CallType.choices)
    anumber = models.CharField(max_length=100, db_index=True)
    bnumber = models.CharField(max_length=100)
    prefix = models.CharField(max_length=100)
    destination = models.CharField(max_length=100)
    location_zone = models.CharField(max_length=100)
    location_code = models.CharField(max_length=100)
    country_code = models.CharField(max_length=3, db_index=True)
    mcc = models.CharField(blank=True, null=True, max_length=10)
    mnc = models.CharField(blank=True, null=True, max_length=10)
    event_time = models.DateTimeField(db_index=True)
    filename = models.CharField(max_length=100)

    class Meta:
        ordering = ['-record_id']
        indexes = [
            models.Index(fields=['msisdn', 'event_time'])
        ]

class FileImportStatus(models.TextChoices):
    IN_PROGRESS = 'in_progress', 'In Progress'
    DONE = 'done', 'Done'
    ERRORED = 'errored', 'Errored'
    SKIPPED_STRANGE = 'skipped_strange', 'Skipped (strange)'
    SKIPPED_IGNORABLE = 'skipped_ignorable', 'Skipped (ignorable)'


class FileImport(models.Model):
    Status = FileImportStatus

    status = models.CharField(max_length=100, choices=FileImportStatus.choices, db_index=True)
    filename = models.CharField(max_length=100, db_index=True, unique=True)
    started = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    cleaned_up = models.BooleanField(default=False, db_index=True)

    @property
    def filename_parts(self):
        all_parts = self.filename.split('_')
        return {
            'cdr': all_parts[0],
            'data_type': all_parts[1],
            'extension': all_parts[-1].split('.', 1)[-1],
            'datestamp': all_parts[-1].split('.', 1)[0],
            'sequence_number': all_parts[-2],
            'mvno': '_'.join(all_parts[2:-2])
        }

    @property
    def data_type(self):
        return self.filename_parts['data_type']

    @property
    def extension(self):
        return self.filename_parts['extension']

    @property
    def mvno(self):
        return self.filename_parts['mvno']

    @property
    def sequence_number(self):
        return int(self.filename_parts['sequence_number'])

    @property
    def datestamp(self):
        return parse_timezone_dt(self.filename_parts['datestamp'])


class GammaFileImport(models.Model):
    status = models.CharField(max_length=100, choices=FileImportStatus.choices, db_index=True)
    filename = models.CharField(max_length=100, db_index=True, unique=True)
    started = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    cleaned_up = models.BooleanField(default=False, db_index=True)


class BaseGammaCDR(models.Model):
    filename = models.CharField(max_length=100, db_index=True)
    call_type = models.CharField(max_length=255, null=True, blank=True)
    call_cause_definition_required = models.CharField(max_length=255, null=True, blank=True)
    customer_identifier = models.CharField(max_length=255, null=True, blank=True)
    non_charged_party = models.CharField(max_length=255, null=True, blank=True)
    call_date = models.CharField(max_length=255, null=True, blank=True)
    call_time = models.CharField(max_length=255, null=True, blank=True)
    duration = models.BigIntegerField(null=True, blank=True)
    bytes_transmitted = models.BigIntegerField(null=True, blank=True)
    bytes_received = models.BigIntegerField(null=True, blank=True)
    description = models.CharField(max_length=255, null=True, blank=True)
    chargecode = models.CharField(max_length=255, null=True, blank=True)
    time_band = models.CharField(max_length=255, null=True, blank=True)
    salesprice = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    salesprice_pre_bundle = models.CharField(max_length=255, null=True, blank=True)
    extension = models.CharField(max_length=255, null=True, blank=True)
    ddi = models.CharField(max_length=255, null=True, blank=True)
    grouping_id = models.CharField(max_length=255, null=True, blank=True)
    call_class_feature = models.CharField(max_length=255, null=True, blank=True)
    carrier = models.CharField(max_length=255, null=True, blank=True)
    recording = models.CharField(max_length=255, null=True, blank=True)
    vat = models.CharField(max_length=255, null=True, blank=True)
    country_of_origin = models.CharField(max_length=255, null=True, blank=True)
    network = models.CharField(max_length=255, null=True, blank=True)
    retail_tariff_code = models.CharField(max_length=255, null=True, blank=True)
    remote_network = models.CharField(max_length=255, null=True, blank=True)
    apn = models.CharField(max_length=255, null=True, blank=True)
    diverted_number = models.CharField(max_length=255, null=True, blank=True)
    ring_time = models.CharField(max_length=255, null=True, blank=True)
    record_id = models.CharField(max_length=255, null=True, blank=True)
    currency = models.CharField(max_length=255, null=True, blank=True)
    caller_line_identity = models.CharField(max_length=255, null=True, blank=True)
    network_access_reference = models.CharField(max_length=255, null=True, blank=True)
    ngcs_access_charge = models.CharField(max_length=255, null=True, blank=True)
    ngcs_service_charge = models.CharField(max_length=255, null=True, blank=True)
    total_bytes_transferred = models.BigIntegerField(null=True, blank=True)
    user_id = models.CharField(max_length=255, null=True, blank=True)
    onward_billing_reference = models.CharField(max_length=255, null=True, blank=True)
    contract_name = models.CharField(max_length=255, null=True, blank=True)
    bundle_name = models.CharField(max_length=255, null=True, blank=True)
    bundle_allowance = models.BigIntegerField(null=True, blank=True)
    discount_reference = models.CharField(max_length=255, null=True, blank=True)
    routing_code = models.CharField(max_length=255, null=True, blank=True)
    # Our additions
    msisdn = models.CharField(max_length=255, db_index=True)
    event_time = models.DateTimeField(db_index=True)

    class Meta:
        abstract = True
        ordering = ['-id']
        indexes = [
            models.Index(fields=['msisdn', 'event_time'])
        ]

class GammaDataCDRQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        return self.filter(event_time__gte=start_date, event_time__lt=end_date)

    def sum_usage(self):
        return self.aggregate(total_usage=models.Sum('data_usage_bytes'))['total_usage']

    def group_by_msisdn(self):
        return self.values('msisdn')

    def annotate_usage(self):
        return self.annotate(total_usage=models.Sum('data_usage_bytes'))

class GammaDataCDR(BaseGammaCDR):
    class RoamingZones(models.TextChoices):
        A = 'a', 'A (UK)'
        E = 'e', 'E (EU)'
        B = 'b', 'B'
        C = 'c', 'C'
        D = 'd', 'D'

        @staticmethod
        def get_by_letter(letter):
            if letter:
                for zone in GammaDataCDR.RoamingZones:
                    if zone[0].lower() == letter.lower():
                        return zone
            return None

    data_usage_bytes = models.BigIntegerField()
    roaming_zone = models.CharField(max_length=255, default=RoamingZones.A, choices=RoamingZones.choices, db_index=True)
    objects = models.Manager.from_queryset(GammaDataCDRQuerySet)()


class GammaVoiceCDRQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        return self.filter(event_time__gte=start_date, event_time__lt=end_date)

    def sum_usage(self):
        return self.aggregate(total_usage=models.Sum('duration_seconds'))['total_usage']

    def annotate_usage(self):
        return self.annotate(total_usage=models.Sum('duration_seconds'))

class GammaVoiceCDR(BaseGammaCDR):
    duration_seconds = models.BigIntegerField()
    objects = models.Manager.from_queryset(GammaVoiceCDRQuerySet)()

class GammaSMSCDRQuerySet(models.QuerySet):
    def date_filter(self, start_date, end_date):
        return self.filter(event_time__gte=start_date, event_time__lt=end_date)

    def sum_usage(self):
        return self.count()

    def annotate_usage(self):
        return self.annotate(total_usage=models.Count('id'))

class GammaSMSCDR(BaseGammaCDR):
    quantity = models.BigIntegerField()
    objects = models.Manager.from_queryset(GammaSMSCDRQuerySet)()
