# Generated by Django 4.2.7 on 2024-09-22 21:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0008_fileimport_cleaned_up_alter_fileimport_filename'),
    ]

    operations = [
        migrations.CreateModel(
            name='GammaFileImport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('errored', 'Errored'), ('skipped_strange', 'Skipped (strange)')], db_index=True, max_length=100)),
                ('filename', models.CharField(db_index=True, max_length=100, unique=True)),
                ('started', models.DateTimeField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('cleaned_up', models.<PERSON><PERSON><PERSON><PERSON>ield(db_index=True, default=False)),
            ],
        ),
        migrations.CreateModel(
            name='GammaVoiceCDR',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('call_cause_definition_required', models.CharField(blank=True, max_length=255, null=True)),
                ('customer_identifier', models.CharField(blank=True, max_length=255, null=True)),
                ('non_charged_party', models.CharField(blank=True, max_length=255, null=True)),
                ('call_date', models.CharField(blank=True, max_length=255, null=True)),
                ('call_time', models.CharField(blank=True, max_length=255, null=True)),
                ('duration', models.IntegerField(blank=True, null=True)),
                ('bytes_transmitted', models.IntegerField(blank=True, null=True)),
                ('bytes_received', models.IntegerField(blank=True, null=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('chargecode', models.CharField(blank=True, max_length=255, null=True)),
                ('time_band', models.CharField(blank=True, max_length=255, null=True)),
                ('salesprice', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('salesprice_pre_bundle', models.CharField(blank=True, max_length=255, null=True)),
                ('extension', models.CharField(blank=True, max_length=255, null=True)),
                ('ddi', models.CharField(blank=True, max_length=255, null=True)),
                ('grouping_id', models.CharField(blank=True, max_length=255, null=True)),
                ('call_class_feature', models.CharField(blank=True, max_length=255, null=True)),
                ('carrier', models.CharField(blank=True, max_length=255, null=True)),
                ('recording', models.CharField(blank=True, max_length=255, null=True)),
                ('vat', models.CharField(blank=True, max_length=255, null=True)),
                ('country_of_origin', models.CharField(blank=True, max_length=255, null=True)),
                ('network', models.CharField(blank=True, max_length=255, null=True)),
                ('retail_tariff_code', models.CharField(blank=True, max_length=255, null=True)),
                ('remote_network', models.CharField(blank=True, max_length=255, null=True)),
                ('apn', models.CharField(blank=True, max_length=255, null=True)),
                ('diverted_number', models.CharField(blank=True, max_length=255, null=True)),
                ('ring_time', models.CharField(blank=True, max_length=255, null=True)),
                ('record_id', models.CharField(blank=True, max_length=255, null=True)),
                ('currency', models.CharField(blank=True, max_length=255, null=True)),
                ('caller_line_identity', models.CharField(blank=True, max_length=255, null=True)),
                ('network_access_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('ngcs_access_charge', models.CharField(blank=True, max_length=255, null=True)),
                ('ngcs_service_charge', models.CharField(blank=True, max_length=255, null=True)),
                ('total_bytes_transferred', models.IntegerField(blank=True, null=True)),
                ('user_id', models.CharField(blank=True, max_length=255, null=True)),
                ('onward_billing_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('contract_name', models.CharField(blank=True, max_length=255, null=True)),
                ('bundle_name', models.CharField(blank=True, max_length=255, null=True)),
                ('bundle_allowance', models.IntegerField(blank=True, null=True)),
                ('discount_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('routing_code', models.CharField(blank=True, max_length=255, null=True)),
                ('msisdn', models.CharField(db_index=True, max_length=255)),
                ('event_time', models.DateTimeField(db_index=True)),
                ('duration_seconds', models.IntegerField()),
            ],
            options={
                'ordering': ['-id'],
                'abstract': False,
                'indexes': [models.Index(fields=['msisdn', 'event_time'], name='core_gammav_msisdn_c5f9b9_idx')],
            },
        ),
        migrations.CreateModel(
            name='GammaSMSCDR',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('call_cause_definition_required', models.CharField(blank=True, max_length=255, null=True)),
                ('customer_identifier', models.CharField(blank=True, max_length=255, null=True)),
                ('non_charged_party', models.CharField(blank=True, max_length=255, null=True)),
                ('call_date', models.CharField(blank=True, max_length=255, null=True)),
                ('call_time', models.CharField(blank=True, max_length=255, null=True)),
                ('duration', models.IntegerField(blank=True, null=True)),
                ('bytes_transmitted', models.IntegerField(blank=True, null=True)),
                ('bytes_received', models.IntegerField(blank=True, null=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('chargecode', models.CharField(blank=True, max_length=255, null=True)),
                ('time_band', models.CharField(blank=True, max_length=255, null=True)),
                ('salesprice', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('salesprice_pre_bundle', models.CharField(blank=True, max_length=255, null=True)),
                ('extension', models.CharField(blank=True, max_length=255, null=True)),
                ('ddi', models.CharField(blank=True, max_length=255, null=True)),
                ('grouping_id', models.CharField(blank=True, max_length=255, null=True)),
                ('call_class_feature', models.CharField(blank=True, max_length=255, null=True)),
                ('carrier', models.CharField(blank=True, max_length=255, null=True)),
                ('recording', models.CharField(blank=True, max_length=255, null=True)),
                ('vat', models.CharField(blank=True, max_length=255, null=True)),
                ('country_of_origin', models.CharField(blank=True, max_length=255, null=True)),
                ('network', models.CharField(blank=True, max_length=255, null=True)),
                ('retail_tariff_code', models.CharField(blank=True, max_length=255, null=True)),
                ('remote_network', models.CharField(blank=True, max_length=255, null=True)),
                ('apn', models.CharField(blank=True, max_length=255, null=True)),
                ('diverted_number', models.CharField(blank=True, max_length=255, null=True)),
                ('ring_time', models.CharField(blank=True, max_length=255, null=True)),
                ('record_id', models.CharField(blank=True, max_length=255, null=True)),
                ('currency', models.CharField(blank=True, max_length=255, null=True)),
                ('caller_line_identity', models.CharField(blank=True, max_length=255, null=True)),
                ('network_access_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('ngcs_access_charge', models.CharField(blank=True, max_length=255, null=True)),
                ('ngcs_service_charge', models.CharField(blank=True, max_length=255, null=True)),
                ('total_bytes_transferred', models.IntegerField(blank=True, null=True)),
                ('user_id', models.CharField(blank=True, max_length=255, null=True)),
                ('onward_billing_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('contract_name', models.CharField(blank=True, max_length=255, null=True)),
                ('bundle_name', models.CharField(blank=True, max_length=255, null=True)),
                ('bundle_allowance', models.IntegerField(blank=True, null=True)),
                ('discount_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('routing_code', models.CharField(blank=True, max_length=255, null=True)),
                ('msisdn', models.CharField(db_index=True, max_length=255)),
                ('event_time', models.DateTimeField(db_index=True)),
                ('quantity', models.IntegerField()),
            ],
            options={
                'ordering': ['-id'],
                'abstract': False,
                'indexes': [models.Index(fields=['msisdn', 'event_time'], name='core_gammas_msisdn_138a9e_idx')],
            },
        ),
        migrations.CreateModel(
            name='GammaDataCDR',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('call_cause_definition_required', models.CharField(blank=True, max_length=255, null=True)),
                ('customer_identifier', models.CharField(blank=True, max_length=255, null=True)),
                ('non_charged_party', models.CharField(blank=True, max_length=255, null=True)),
                ('call_date', models.CharField(blank=True, max_length=255, null=True)),
                ('call_time', models.CharField(blank=True, max_length=255, null=True)),
                ('duration', models.IntegerField(blank=True, null=True)),
                ('bytes_transmitted', models.IntegerField(blank=True, null=True)),
                ('bytes_received', models.IntegerField(blank=True, null=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('chargecode', models.CharField(blank=True, max_length=255, null=True)),
                ('time_band', models.CharField(blank=True, max_length=255, null=True)),
                ('salesprice', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('salesprice_pre_bundle', models.CharField(blank=True, max_length=255, null=True)),
                ('extension', models.CharField(blank=True, max_length=255, null=True)),
                ('ddi', models.CharField(blank=True, max_length=255, null=True)),
                ('grouping_id', models.CharField(blank=True, max_length=255, null=True)),
                ('call_class_feature', models.CharField(blank=True, max_length=255, null=True)),
                ('carrier', models.CharField(blank=True, max_length=255, null=True)),
                ('recording', models.CharField(blank=True, max_length=255, null=True)),
                ('vat', models.CharField(blank=True, max_length=255, null=True)),
                ('country_of_origin', models.CharField(blank=True, max_length=255, null=True)),
                ('network', models.CharField(blank=True, max_length=255, null=True)),
                ('retail_tariff_code', models.CharField(blank=True, max_length=255, null=True)),
                ('remote_network', models.CharField(blank=True, max_length=255, null=True)),
                ('apn', models.CharField(blank=True, max_length=255, null=True)),
                ('diverted_number', models.CharField(blank=True, max_length=255, null=True)),
                ('ring_time', models.CharField(blank=True, max_length=255, null=True)),
                ('record_id', models.CharField(blank=True, max_length=255, null=True)),
                ('currency', models.CharField(blank=True, max_length=255, null=True)),
                ('caller_line_identity', models.CharField(blank=True, max_length=255, null=True)),
                ('network_access_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('ngcs_access_charge', models.CharField(blank=True, max_length=255, null=True)),
                ('ngcs_service_charge', models.CharField(blank=True, max_length=255, null=True)),
                ('total_bytes_transferred', models.IntegerField(blank=True, null=True)),
                ('user_id', models.CharField(blank=True, max_length=255, null=True)),
                ('onward_billing_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('contract_name', models.CharField(blank=True, max_length=255, null=True)),
                ('bundle_name', models.CharField(blank=True, max_length=255, null=True)),
                ('bundle_allowance', models.IntegerField(blank=True, null=True)),
                ('discount_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('routing_code', models.CharField(blank=True, max_length=255, null=True)),
                ('msisdn', models.CharField(db_index=True, max_length=255)),
                ('event_time', models.DateTimeField(db_index=True)),
                ('data_usage_bytes', models.IntegerField()),
            ],
            options={
                'ordering': ['-id'],
                'abstract': False,
                'indexes': [models.Index(fields=['msisdn', 'event_time'], name='core_gammad_msisdn_14a333_idx')],
            },
        ),
    ]
