import time
import json
import hmac
import hashlib
import logging
import requests
from django.conf import settings
from provider.models import Pollable
from core.slack import send_debug_slack_message
from .clients import GammaClient

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def poll_all():
    pollables = Pollable.get_all_pollable()
    client = GammaClient()
    for pollable in pollables:
        Poller(pollable, client).poll()


class Poller:
    def __init__(self, pollable, client):
        self.pollable = pollable
        self.pollable_type = self.pollable.polling_type
        self.params = self.pollable.params
        self.client = client

    def _notify_callback(self, **kwargs):
        logger.info('Notifying callback for %s, %s', self.pollable_type, self.params)
        response_data = {
            'event_type': self.pollable_type,
            **self.params,
            **kwargs
        }
        json_data = json.dumps(response_data, sort_keys=True)
        hmac_digest = hmac.new(settings.GAMMA_WEBHOOK_SECRET_KEY.encode('utf-8'), json_data.encode('utf-8'), hashlib.sha256).hexdigest()
        headers = {
            'X-GAMMA-WHSIG-256': hmac_digest,
            'Content-Type': 'application/json'
        }
        response = requests.post(self.params['callback_url'], data=json_data, headers=headers, timeout=20)
        return response

    def _notify_callback_and_complete(self, **kwargs):
        response = self._notify_callback(**kwargs)
        self.complete()
        response.raise_for_status()

    def complete(self):
        self.pollable.status = Pollable.PollingStatusChoices.COMPLETED
        self.pollable.save()

    @property
    def poll_limit(self):
        if self.pollable_type == Pollable.PollingTypeChoices.PORT:
            return 24*60*30
        return 24*60

    def poll(self):  # pylint: disable=too-many-branches, too-many-statements, too-many-return-statements
        logger.info('Polling for %s, %s', self.pollable_type, self.params)
        self.pollable.poll_count += 1
        self.pollable.save()
        if self.pollable.poll_count > self.poll_limit:
            self.pollable.status = Pollable.PollingStatusChoices.TIMED_OUT
            self.pollable.save()
            logger.error('Polling timeout for %s', self.pollable.pk)
            send_debug_slack_message(f'Polling timed out for {self.pollable_type}, {self.params}')
            return
        try:
            if self.pollable_type in [Pollable.PollingTypeChoices.SERVICE_PROVISIONED, Pollable.PollingTypeChoices.SERVICE_ACTIVATED, Pollable.PollingTypeChoices.SERVICE_ON_PLAN, Pollable.PollingTypeChoices.SERVICE_BARRED, Pollable.PollingTypeChoices.SERVICE_UNBARRED, Pollable.PollingTypeChoices.SERVICE_CANCELLED]:
                service_id = self.params['service_id']
                try:
                    response = self.client.get_service(service_id)
                except requests.HTTPError as e:
                    if e.response.status_code == 401:
                        self.client = GammaClient()
                        response = self.client.get_service(service_id)
                    elif e.response.status_code == 404 and self.pollable_type == Pollable.PollingTypeChoices.SERVICE_CANCELLED:
                        self._notify_callback_and_complete()
                        return
                    else:
                        raise
                if response['status'] == 'ACTIVE' and self.pollable_type == Pollable.PollingTypeChoices.SERVICE_ACTIVATED:
                    self._notify_callback_and_complete()
                    return
                elif response['status'] == 'SET_UP' and self.pollable_type == Pollable.PollingTypeChoices.SERVICE_PROVISIONED:
                    self._notify_callback_and_complete()
                    return
                elif self.pollable_type == Pollable.PollingTypeChoices.SERVICE_ON_PLAN and self.params['gamma_package_id'] in response['configuration']['boltOns']:
                    self._notify_callback_and_complete()
                    return
                elif self.pollable_type == Pollable.PollingTypeChoices.SERVICE_BARRED and response['configuration'].get('networkServices', []) == []:
                    self._notify_callback_and_complete()
                    return
                elif self.pollable_type == Pollable.PollingTypeChoices.SERVICE_UNBARRED and 'MOBILE_DATA' in response['configuration'].get('networkServices', []):
                    self._notify_callback_and_complete()
                    return
                elif self.pollable_type == Pollable.PollingTypeChoices.SERVICE_CANCELLED and not response or response['status'] == 'CANCELLED':
                    self._notify_callback_and_complete()
                    return
            elif self.pollable_type == Pollable.PollingTypeChoices.PORT:
                last_poll_time = self.params.get('last_port_poll', 0)
                if time.time() - last_poll_time > 3600:
                    status = self.client.get_port_status(self.params['msisdn'], self.params['pac'])
                    if self.params.get('last_status', '') != status:
                        self._notify_callback(status=status)
                        if status in ['EXPIRED', 'CANCELLED', 'ARCHIVED']:
                            self.complete()
                        elif status == 'LOCKED':
                            service = self.client.get_service_by_msisdn(self.params['msisdn'])
                            if 'porting' not in service:
                                self.complete()
                        else:
                            self.params['last_status'] = status
                            self.params['last_port_poll'] = time.time()
                            self.pollable.params = self.params
                            self.pollable.save()
                    else:
                        self.params['last_port_poll'] = time.time()
                        self.pollable.params = self.params
                        self.pollable.save()
                return
            else:
                raise RuntimeError(f'Unknown result type {self.pollable_type}')
            logger.info('Polling %s, %s, %s, %s', self.pollable_type, self.params, response, self.pollable.poll_count)
        except Exception as e:  # pylint: disable=broad-except
            logger.exception('Error polling for %s, %s: %s', self.pollable_type, self.params, e)
            send_debug_slack_message(f'Error polling for {self.pollable_type}, {self.params}: {e}')
            self.pollable.error_count += 1
            if self.pollable.error_count > 3:
                self.pollable.status = Pollable.PollingStatusChoices.ERRORED
            self.pollable.save()
