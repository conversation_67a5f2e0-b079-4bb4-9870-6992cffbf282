import * as fs from 'fs';
import * as path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import eslint from 'vite-plugin-eslint';
import { TanStackRouterVite } from '@tanstack/router-vite-plugin';
import { viteStaticCopy } from 'vite-plugin-static-copy';

// this can be derived from the filesystem if I could be bothered
const custom_clients = {
  yayzi: {
    index: 'src/clients/yayzi/index.html'
  },
  richmond: {
    index: 'src/clients/richmond/index.html'
  },
  itsi: {
    index: 'src/clients/itsi/index.html'
  },
  cove: {
    index: 'src/clients/cove/index.html'
  },
  perkfon: {
    index: 'src/clients/perkfon/index.html'
  },
  millwall: {
    index: 'src/clients/millwall/index.html'
  },
  default: {
    index: './index.html'
  }
};

const noFollowClients = ['richmond', 'default'];

// https://vitejs.dev/config/
export default ({ mode }) => {
  // this gathers the environment variables from the .env files
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  const envClient = process.env
    .VITE_CUSTOM_CLIENT as keyof typeof custom_clients;

  const client = Object.keys(custom_clients).includes(envClient)
    ? envClient
    : 'default';

  const isClientNoFollow = noFollowClients.includes(client);

  return defineConfig({
    plugins: [
      react(),
      eslint(),
      TanStackRouterVite(),
      viteStaticCopy({
        targets: [
          {
            src: `./public/${client}` + '/[!.]*',
            dest: './'
          },
          {
            src: isClientNoFollow
              ? 'src/config/robots/robots.no-follow.txt'
              : 'src/config/robots/robots.default.txt',
            dest: '', // Copies to the root of the public directory
            rename: 'robots.txt' // Rename it to robots.txt
          }
        ]
      }),
      {
        name: 'copy-custom-client-html-to-default-location',
        writeBundle(options: { dir: string }) {
          if (client != 'default') {
            fs.copyFile(
              path.join(options.dir, custom_clients[client].index),
              path.join(options.dir, custom_clients['default'].index),
              (err) => {
                if (err) throw err;
              }
            );
          }
        }
      },
      {
        name: 'index-html-build-replacement',
        apply: 'serve',
        transformIndexHtml: {
          order: 'pre',
          handler: async () => {
            return fs.readFileSync(custom_clients[client].index, {
              encoding: 'utf-8'
            });
          }
        }
      }
    ],
    build: {
      rollupOptions: {
        input: {
          app: custom_clients[client].index
        }
      }
    },
    publicDir: `public/default`,
    resolve: {
      alias: {
        src: '/src',
        components: '/src/components'
      }
    },
    server: {
      port: 3000
    }
  });
};
