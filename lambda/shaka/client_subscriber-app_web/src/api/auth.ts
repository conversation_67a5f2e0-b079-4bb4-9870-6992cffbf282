import api from './index';

type SignUpReturn = {
  AccessToken: string;
  ExpiresIn: number;
  IdToken: string;
  RefreshToken: string;
  TokenType: string;
};

export const login = (payload: { email: string; password: string }) =>
  api.post('/auth/login/', payload).then((res) => res.data);

export const signup = (
  payload: Record<string, string>
): Promise<SignUpReturn> =>
  api.post('/auth/sign-up/', payload).then((res) => res.data);

export const sendForgotPassword = (payload: { email: string }) =>
  api.post('/auth/forgot-password/', payload).then((res) => res.data);

export const confirmForgotPassword = (payload: {
  verification_code: string;
  email: string;
  new_password: string;
}) =>
  api.post('/auth/confirm-forgot-password/', payload).then((res) => res.data);

export const changePassword = (payload: {
  old_password: string;
  new_password: string;
}) => api.post('/auth/change-password/', payload).then((res) => res.data);

export const verify = (payload: { code: string }) =>
  api.post('/auth/verify/', payload).then((res) => res.data);

export const resendVerificationCode = () =>
  api.post('/auth/verify/resend/').then((res) => res.data);

export const sendEsimInstructions = (email: string) =>
  api.post('auth/instructions/', { email }).then((res) => res.data);
