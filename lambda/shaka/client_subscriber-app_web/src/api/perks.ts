import api from './index';
import { Perk } from 'src/types/perks';

export const fetchPerks = (): Promise<{ perks: Perk[]; balance: number }> =>
  api.get('/data/perks/').then((res) => res.data);

export const claimPerk = (payload: {
  id: string | number;
  claim_with_points?: boolean;
}): Promise<{ perks: Perk[]; balance: number }> => {
  return api.post('/data/claim-perk/', payload).then((res) => res.data);
};
