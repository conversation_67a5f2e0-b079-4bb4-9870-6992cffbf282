type VoucherDetails = {
  merchant_name: string;
  code: string;
  url?: string;
  instructions?: string;
  expiry_date?: string;
};

export type Perk = {
  id: number;
  title: string;
  description: string;
  image: string;
  eligibilityType:
    | 'airdrop'
    | 'tenure'
    | 'total_spend'
    | 'total_points_earned'
    | 'no_free';
  type: 'bolt-on' | 'voucher' | 'discount';
  cost: number;
  progress: number;
  isClaimed: boolean;
  claimHistory: {
    date: string;
    paidWithPoints: boolean;
  }[];
  remaining: number;
  isRedemptionLimit: boolean;
  multiplyRedeemable: boolean;
  enabled: boolean;
  featured?: boolean;
  amount?: string;
  availabilityDate: string;
  details: VoucherDetails | null;
};
