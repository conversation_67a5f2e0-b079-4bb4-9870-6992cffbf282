import { PropsWithChildren, createContext, useEffect, useState } from 'react';

import { fetchClient } from 'src/api/data';
import { Branding } from 'src/types/branding';
import { PointsNaming } from 'src/types/client';
import { ClientPlan } from 'src/types/plans';

export const ClientContext = createContext<{
  branding: Branding | null;
  isLoading: boolean;
  getPlanById: (id: number | string) => ClientPlan | undefined;
  pointsNaming: PointsNaming;
  clientName: string | null;
}>({
  branding: null,
  isLoading: true,
  getPlanById: () => undefined,
  pointsNaming: {
    singular: 'point',
    plural: 'points'
  },
  clientName: null
});

export const ClientProvider = ({ children }: PropsWithChildren) => {
  const [branding, setBranding] = useState<Branding | null>(null);
  const [clientName, setClientName] = useState<string | null>(null);
  const [plans, setPlans] = useState<ClientPlan[] | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [pointsNaming, setPointsNaming] = useState<PointsNaming>({
    singular: 'point',
    plural: 'points'
  });

  const getPlanById = (id: number | string) => {
    return plans?.find((plan) => plan.id === id);
  };

  useEffect(() => {
    if (!branding) {
      fetchClient()
        .then((res) => {
          setBranding(res.branding);
          setPlans(res.plans);
          setClientName(res.spn);
          setPointsNaming({
            singular: res.perk_point_name_singular,
            plural: res.perk_point_name_plural
          });
        })
        .catch(() => {
          setBranding(null);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [branding]);

  return (
    <ClientContext.Provider
      value={{
        branding,
        getPlanById,
        isLoading,
        pointsNaming,
        clientName
      }}
    >
      {children}
    </ClientContext.Provider>
  );
};
