import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { CheckGreenIcon } from 'src/assets/icons/CheckGreen';
import Button from 'src/components/common/Button';
import ErrorText from 'src/components/common/ErrorText';
import withDrawer from 'src/hocs/withDrawer';
import { schema, ShareEsimInputs } from 'src/schemas/share-sim';

function ShareEsimDrawerContent({
  onShareEsim,
  error
}: {
  onShareEsim: (data: ShareEsimInputs) => void;
  error: string | null;
}) {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<ShareEsimInputs>({
    resolver: yupResolver(schema)
  });

  const onSubmit = (data: ShareEsimInputs) => {
    onShareEsim(data);
  };

  return (
    <form className="px-5" onSubmit={handleSubmit(onSubmit)}>
      <div className="space-y-1 mb-6">
        <label className="block text-xs font-semibold text-[#858585] mb-1">
          Email address of the recipient of this eSIM
        </label>
        <input
          className="input"
          placeholder="Enter email"
          {...register('email')}
        />
        <ErrorText>{errors.email?.message}</ErrorText>
      </div>

      <p className="bg-white text-xs px-3 py-3.5 rounded-2xl flex gap-3 items-center font-semibold border border-[#1EC25F] mb-10">
        <CheckGreenIcon className="min-w-8 h-8" />
        Tell the recipient to follow the installation instructions in their
        mailbox
      </p>

      <ErrorText>{error}</ErrorText>
      <Button color="black" fullWidth size="small">
        Share eSIM
      </Button>
    </form>
  );
}

const ShareEsimDrawer = withDrawer(ShareEsimDrawerContent);

export default ShareEsimDrawer;
