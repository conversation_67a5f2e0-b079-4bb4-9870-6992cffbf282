import { QrExample } from 'src/assets/images/QrExample';
import { FingerTapIcon } from 'src/assets/icons/FingerTap';
import IosSecondStep from './IosSecondStep';
import WhiteBlock from 'src/components/common/WhiteBlock';
import InternetWarning from './InternetWarning';

export default function OldIOS() {
  return (
    <div className="space-y-5">
      <WhiteBlock title="Open email to install eSIM" label="step 1">
        <InternetWarning />
        <div className="flex gap-5 items-center">
          <QrExample className="size-[80px]" />
          <div className="flex flex-col gap-1">
            <FingerTapIcon />
            <span className="text-xs font-semibold">
              Press and hold the QR code in your email
            </span>
            <span className="text-[10px]">Then tap “Add eSIM”</span>
          </div>
        </div>
      </WhiteBlock>
      <IosSecondStep />
    </div>
  );
}
