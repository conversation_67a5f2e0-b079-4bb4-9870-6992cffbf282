import { FingerTapIcon } from 'src/assets/icons/FingerTap';
import WhiteBlock from 'src/components/common/WhiteBlock';

export default function IosSecondStep({
  label = 'Step 2',
  labelColor
}: {
  label?: string;
  labelColor?: string;
}) {
  return (
    <WhiteBlock title="Set up data" label={label} labelColor={labelColor}>
      <div className="text-sm my-2">
        <p className="font-semibold">In your settings head to:</p>
        <p className="italic">
          Mobile Service &gt; Click on the new SIM profile (most likely labelled
          as "Secondary" or "Business") &gt; Mobile Data Network
        </p>
      </div>
      <div className="mt-3">
        <div className="h-[100px] m-auto">
          <img src="/apn-setup.png" className="w-full h-full object-contain" />
        </div>
        <div className="text-xs flex gap-2 items-center justify-center text-[#1EC25F] mt-1">
          <FingerTapIcon />
          <span className="font-semibold">Tap to change</span>
        </div>

        <p className="text-sm mt-2">
          Then, change the Mobile Data APN field from “Three” to “gamma”
        </p>
      </div>
    </WhiteBlock>
  );
}
