import { Link, LinkProps } from "@tanstack/react-router";
import clsx from "clsx";
import { PropsWithChildren } from "react";

interface Props {
  to: LinkProps["to"];
  disabled?: boolean;
}

const SupportLink = ({
  to,
  children,
  disabled,
}: PropsWithChildren<Props>) => (
  <Link
    to={to}
    className={clsx(
      "flex items-center gap-2 bg-white rounded-lg px-2.5 py-2.5 text-xs font-semibold",
      "hover:bg-white/50",
      disabled && "pointer-events-none opacity-50"
    )}
  >
    {children}
  </Link>
);

export default SupportLink;
