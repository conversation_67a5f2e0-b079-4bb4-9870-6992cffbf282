import React, { LegacyRef } from "react";
import { countries } from "src/config/countries";
import InputBlock from "./InputBlock";
import clsx from "clsx";
import { ChevronDownIcon } from "src/assets/icons/ChevronDown";

interface Props extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  required?: boolean;
}

const CountrySelector = React.forwardRef(function Input(
  { label, error, required, disabled, ...props }: Props,
  ref: LegacyRef<HTMLSelectElement>
) {
  return (
    <InputBlock
      name={props.name}
      label={label}
      error={error}
      required={required}
    >
      <select
        ref={ref}
        className={clsx(
          "input",
          "border-r-8 border-transparent",
          disabled && "opacity-50"
        )}
        disabled={disabled}
        {...props}
      >
        <option value="">-- Select --</option>
        {countries.map((country) => (
          <option key={country.value} value={country.value}>
            {country.label}
          </option>
        ))}
      </select>
      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
        <ChevronDownIcon className="size-4 opacity-70" />
      </span>
    </InputBlock>
  );
});

export default CountrySelector;
