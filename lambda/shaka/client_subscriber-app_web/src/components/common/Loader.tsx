type LoaderSize = "small" | "medium" | "large";

const getSize = (size?: LoaderSize) => {
  switch (size) {
    case "large":
      return 64;
    case "medium":
      return 48;
    case "small":
    default:
      return 24;
  }
};

const Loader = ({ size }: { size?: LoaderSize }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={`${getSize(size)}px`}
      height={`${getSize(size)}px`}
      viewBox="0 0 100 100"
    >
      <circle
        cx="50"
        cy="50"
        fill="none"
        opacity={0.7}
        stroke="currentColor"
        strokeWidth="10"
        r="35"
        strokeDasharray="164.93361431346415 56.97787143782138"
      >
        <animateTransform
          attributeName="transform"
          type="rotate"
          repeatCount="indefinite"
          dur="1s"
          values="0 50 50;360 50 50"
          keyTimes="0;1"
        ></animateTransform>
      </circle>
    </svg>
  );
};

export default Loader;
