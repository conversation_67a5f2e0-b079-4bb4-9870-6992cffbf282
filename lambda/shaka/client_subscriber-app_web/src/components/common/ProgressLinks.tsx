import { useNavigate } from '@tanstack/react-router';
import BackButton from './BackButton';
import Button from './Button';
import { RightArrowIcon } from 'src/assets/icons';
import clsx from 'clsx';
import { MouseE<PERSON>, MouseEventHandler } from 'react';

interface Props {
  hideBackButton?: boolean;
  hideNextButton?: boolean;
  disabledNext?: boolean;
  backTo?: string;
  nextTo?: string;
  absolute?: boolean;
  onNextClick?: (e: MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  isLoading?: boolean;
  onBackClick?: MouseEventHandler<HTMLButtonElement>;
}

const ProgressLinks = ({
  hideBackButton,
  hideNextButton,
  disabledNext,
  backTo,
  nextTo,
  absolute,
  onNextClick,
  isLoading,
  onBackClick
}: Props) => {
  const navigate = useNavigate();

  const handleNextCLick: MouseEventHandler<HTMLButtonElement> = (e) => {
    if (onNextClick) {
      onNextClick(e as unknown as <PERSON><PERSON><PERSON><HTMLButtonElement, MouseEvent>);
    }

    if (nextTo) {
      navigate({ to: nextTo });
    }
  };

  return (
    <>
      {!hideBackButton && (
        <div
          className={clsx(
            'bottom-0 left-0',
            absolute ? 'absolute' : 'fixed md:left-max-nav p-6 pl-5'
          )}
        >
          <BackButton size="medium" to={backTo} onClick={onBackClick} />
        </div>
      )}
      {!hideNextButton && (
        <div
          className={clsx(
            'bottom-0 right-0',
            absolute ? 'absolute' : 'fixed md:right-max-nav p-6 pr-5'
          )}
        >
          <Button
            type="submit"
            color="secondary"
            rightIcon={<RightArrowIcon />}
            disabled={disabledNext}
            onClick={handleNextCLick}
            narrow
            fullWidth
            transparentBg
            isLoading={isLoading}
          >
            Next
          </Button>
        </div>
      )}
    </>
  );
};

export default ProgressLinks;
