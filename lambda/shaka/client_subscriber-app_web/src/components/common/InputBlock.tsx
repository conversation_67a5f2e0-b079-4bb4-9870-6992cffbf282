import { PropsWithChildren, ReactNode } from 'react';
import ErrorText from './ErrorText';
import ValidationMark from './ValidationMark';
import { twMerge } from 'tailwind-merge';

export interface InputBlockProps {
  label?: string;
  error?: string;
  name?: string;
  labelAction?: ReactNode;
  required?: boolean;
  description?: ReactNode;
  dirty?: boolean;
  withValidationMark?: boolean;
  hideErrorMessage?: boolean;
}

const InputBlock = ({
  label,
  error,
  name,
  children,
  labelAction,
  required,
  description,
  dirty,
  withValidationMark,
  hideErrorMessage
}: PropsWithChildren<InputBlockProps>) => {
  return (
    <div className="relative">
      {label && (
        <div className="flex items-center justify-between mb-2">
          <label
            htmlFor={name}
            className="block text-sm font-medium leading-6 text-[#858585]"
          >
            {label} {required && <span className="text-red-600">*</span>}
          </label>
          {labelAction}
        </div>
      )}
      <div
        className={twMerge(
          'relative',
          withValidationMark && 'flex gap-2 items-center'
        )}
      >
        <div className="grow">{children}</div>
        {withValidationMark && <ValidationMark error={error} dirty={dirty} />}
      </div>
      {description && (
        <div className="mt-3 text-xs text-gray-500">{description}</div>
      )}
      {error && !hideErrorMessage && <ErrorText>{error}</ErrorText>}
    </div>
  );
};

export const inputClass =
  'block w-full h-9 rounded-md border-0 py-1.5 px-2 text-gray-900 shadow-sm ring-1 ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6';

export default InputBlock;
