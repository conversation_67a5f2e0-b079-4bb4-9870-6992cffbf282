export default function WhiteBlock({
  children,
  title,
  label,
  labelColor
}: {
  children: React.ReactNode;
  title?: string;
  label?: string | number;
  labelColor?: string;
}) {
  return (
    <div className="bg-white rounded-xl p-5">
      {title && (
        <div className="flex items-start justify-between mb-4">
          <h3 className="text-xl font-bold max-w-[80%]">{title}</h3>
          {label && (
            <span
              className="text-[10px] rounded-full bg-black uppercase text-white py-0.5 px-2 font-semibold mt-1"
              style={labelColor ? { backgroundColor: labelColor } : {}}
            >
              {label}
            </span>
          )}
        </div>
      )}
      {children}
    </div>
  );
}
