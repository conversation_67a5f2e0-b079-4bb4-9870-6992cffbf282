import { currencyRounded } from 'src/helpers';
import { RoamingBoltOn } from 'src/types/boltons';
import { getTimeDifferenceFromNow } from 'src/helpers/date';
import { twJoin } from 'tailwind-merge';

export default function ActiveBolton({
  title,
  data,
  price,
  voice,
  sms,
  valid_until: validUntil
}: RoamingBoltOn) {
  return (
    <div className="bg-white rounded-xl px-5 py-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">{title}</h2>
        <span className="text-primary text-lg font-semibold">
          {currencyRounded(price)}
        </span>
      </div>
      <p className="mt-2 mb-8">
        Valid for the next{' '}
        <span className="font-semibold text-sm">
          {getTimeDifferenceFromNow(validUntil)}
        </span>
      </p>

      <p className="font-bold mb-2">Remaining allowance:</p>
      <ul className="mb-2">
        <li>
          {Number(data) === 0 ? (
            <span className="text-error font-bold">0</span>
          ) : (
            <span className="font-bold">{data}GB</span>
          )}{' '}
          data
        </li>
        <li>
          <span
            className={twJoin('font-bold', Number(voice) === 0 && 'text-error')}
          >
            {voice}
          </span>{' '}
          mins (inbound & outbound)
        </li>
        <li>
          <span
            className={twJoin('font-bold', Number(sms) === 0 && 'text-error')}
          >
            {sms}
          </span>{' '}
          texts
        </li>
      </ul>
    </div>
  );
}
