import { ROUTES } from 'src/config/routes';
import Link from '../common/Link';
import { useEffect, useState } from 'react';
import Drawer from '../common/Drawer';
import { useNavigate } from '@tanstack/react-router';
import Dialog from '../common/Dialog';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import Pride from 'react-canvas-confetti/dist/presets/pride';
import { TConductorInstance } from 'react-canvas-confetti/dist/types';
import { getConfettiOptions } from 'src/config/confetti-animation';
import useSubscription from 'src/hooks/useSubscription';

const Content = () => {
  const { fetchSubscriber } = useSubscription();

  const [conductor, setConductor] = useState<TConductorInstance>();

  const onInitConfetti = ({ conductor }: { conductor: TConductorInstance }) => {
    setConductor(conductor);
  };

  useEffect(() => {
    if (conductor) {
      conductor.shoot();
    }
  }, [conductor]);

  useEffect(() => {
    fetchSubscriber();
  }, [fetchSubscriber]);

  return (
    <div className="px-10 h-[600px] flex flex-col justify-center gap-10 items-center">
      <div className="validation valid">
        <CheckRoundedFilledIcon className="size-12" />
      </div>
      <div className="mx-7">
        <div className="space-y-4 mb-16 text-sm text-center">
          You subscribed to the bolt on successfully.
        </div>

        <Link
          to={ROUTES.Dashboard}
          color="secondary"
          styleType="button"
          replace
        >
          Go to dashboard
        </Link>
      </div>
      <div className="absolute inset-0 pointer-events-none z-30">
        <Pride
          onInit={onInitConfetti}
          globalOptions={{ resize: true }}
          decorateOptions={getConfettiOptions(1)}
          style={{ width: '100%', height: '100%' }}
        />
      </div>
    </div>
  );
};

export default function BoltonPaymentResult() {
  const title = 'Bolt ons';
  const [isMobile] = useState(window.innerWidth < 640);
  const [open, setOpen] = useState(true);

  const navigate = useNavigate();

  const handleClose = () => {
    setOpen(false);
    setTimeout(() => {
      navigate({ to: '/settings' });
    }, 300);
  };

  return (
    <>
      {isMobile ? (
        <Drawer
          title={title}
          isOpen={open}
          onClose={handleClose}
          adaptiveHeight
        >
          <Content />
        </Drawer>
      ) : (
        <Dialog
          title={title}
          isOpen={open}
          onClose={handleClose}
          panelStyles="bg-[#F3F3F3]"
        >
          <div className="relative max-h-[800px] overflow-y-auto h-[70vh] text-black">
            <Content />
          </div>
        </Dialog>
      )}
    </>
  );
}
