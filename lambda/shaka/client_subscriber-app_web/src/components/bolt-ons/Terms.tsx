import Button from '../common/Button';
import Dialog from '../common/Dialog';

const paragraphs = [
  'Introduction: These terms and conditions govern the use of our mobile network services. By using our services, you agree to comply with and be bound by these terms.',
  'Service Provision: We commit to providing a high-quality mobile network service. However, service may be affected by factors outside our control, including weather, obstructions, and network congestion.',
  'Usage: The customer agrees to use the services in accordance with all applicable laws and regulations. Unauthorised use, including fraud and abuse, is prohibited.',
  'Charges and Billing: By subscribing to our network, you agree to pay all charges incurred under your account, including those resulting from unauthorised use. Bills are issued monthly and must be paid by the due date.',
  'Fair Usage Policy: Unlimited plans are subject to a fair usage policy. Excessive usage may result in reduced service speeds or additional charges.',
  'Roaming: Roaming services are subject to additional charges. Customers are advised to review roaming rates before traveling abroad.',
  'Data Protection: We process personal data in accordance with our Privacy Policy and applicable data protection laws. Your data will be used to manage your account and provide our services.',
  'Contract Termination: Either party may terminate the contract by providing 30 days’ notice. Early termination fees may apply if you cancel before the end of your contract period.',
  'Equipment: Customers are responsible for the care and maintenance of any equipment provided. Lost or damaged equipment may incur additional charges.',
  'Liability: We do not accept liability for any loss or damage suffered due to the interruption of services, except where caused by our negligence.',
  'Amendments: We reserve the right to amend these terms and conditions. Customers will be notified of any significant changes at least one month in advance.',
  'Complaints: Should you have any complaints about our service, you may contact our customer service department. We aim to resolve all complaints promptly.',
  'Governing Law: These terms and conditions are governed by and construed in accordance with the laws of England and Wales.',
  'Dispute Resolution: Any disputes arising from these terms and conditions will be subject to the exclusive jurisdiction of the courts of England and Wales.',
  'Service Interruptions: We may occasionally perform maintenance on the network that could temporarily disrupt services. Efforts will be made to minimize any inconvenience.',
  'SIM Cards: Customers must inform us if their SIM card is lost or stolen. The customer remains responsible for all charges until the loss is reported.',
  'Promotions: Any promotions or offers are subject to their own specific terms and conditions. We reserve the right to withdraw promotions at our discretion.',
  'Customer Responsibilities: You must ensure that all details provided are up to date and inform us of any changes to your contact information.',
  'Support: Our customer support team is available to assist with technical issues and account inquiries during business hours stated on our website.',
  'Entire Agreement: These terms and conditions constitute the entire agreement between the customer and our mobile network service, superseding all prior agreements and understandings.'
];

export default function Terms({
  isOpen,
  onClose
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  return (
    <Dialog
      title="Bolt-on Terms & Conditions"
      isOpen={isOpen}
      onClose={onClose}
      hideCloseButton
    >
      <div className="overflow-auto max-h-[60vh]">
        <ol className="list-decimal ml-6">
          {paragraphs.map((paragraph, index) => (
            <li key={index} className="mb-3">
              {paragraph}
            </li>
          ))}
        </ol>
      </div>
      <div className="w-40 m-auto mt-5">
        <Button color="gray" squared onClick={onClose}>
          Done
        </Button>
      </div>
    </Dialog>
  );
}
