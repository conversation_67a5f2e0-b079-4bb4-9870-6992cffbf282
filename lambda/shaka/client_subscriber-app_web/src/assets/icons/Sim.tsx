export function SimIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="101"
      height="148"
      viewBox="0 0 101 148"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.72711 48.6883C6.81794 48.7153 6.90893 48.7396 7 48.7612V51.6662C6.80611 51.7061 6.61251 51.7579 6.42005 51.822C4.18628 52.5666 2.68606 54.1988 1.92401 55.1613C0.314284 57.1946 0 59.5165 0 61.1207L0 136.416C0 142.814 5.18602 148 11.5833 148H11.9415C11.9609 148 11.9805 148 12 148H89C89.0195 148 89.0391 148 89.0585 148H89.1726C95.5698 148 100.756 142.814 100.756 136.416L100.756 61.1207C100.756 59.5165 100.442 57.1946 98.8318 55.1613C98.0698 54.1988 96.5696 52.5666 94.3358 51.822C94.2242 51.7848 94.1122 51.7518 94 51.7228V48.5234C98 47.2506 100.756 43.5252 100.756 39.2791V25.427C100.756 22.718 99.6234 20.1324 97.6322 18.2956L80.5861 2.57091C78.7942 0.91784 76.4455 0 74.0075 0L9.70234 0C4.34389 0 0 4.34387 0 9.70233L0 39.6716C0 43.8347 2.7365 47.5025 6.72711 48.6883Z"
        fill="black"
      />
      <rect x="12" y="65" width="51" height="71" rx="12" fill="white" />
    </svg>
  );
}
