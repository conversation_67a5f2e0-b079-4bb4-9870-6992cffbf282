export function CheckRoundedFilledIcon({
  circleColor,
  ...props
}: React.SVGProps<SVGSVGElement> & { circleColor?: string }) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <circle cx="24" cy="24" r="24" fill={circleColor || 'white'} />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M35.5092 18.1466C36.3592 17.0838 36.187 15.5331 35.1241 14.6829C34.0615 13.8327 32.5107 14.005 31.6605 15.0677L20.0097 29.6312L15.3491 26.1357C14.2603 25.3191 12.7157 25.5398 11.8991 26.6286C11.0825 27.7174 11.3031 29.262 12.3919 30.0786L18.9634 35.0071C20.0282 35.8059 21.5348 35.6143 22.3662 34.5751L35.5092 18.1466Z"
        fill="currentColor"
      />
    </svg>
  );
}
