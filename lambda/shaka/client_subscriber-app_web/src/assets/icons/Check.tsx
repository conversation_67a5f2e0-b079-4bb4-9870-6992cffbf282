import React from 'react';

export function CheckIcon(
  props: React.SVGProps<SVGSVGElement> & { color?: string }
) {
  return (
    <svg
      viewBox="0 0 43 43"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_204_1830)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M41.8853 3.68094C43.1928 4.76153 43.3771 6.69751 42.2962 8.00511L17.6021 37.8878L17.5946 37.8967C17.0329 38.5712 16.328 39.1121 15.531 39.4797C14.734 39.8477 13.8651 40.0332 12.9875 40.023C12.0954 40.012 11.2147 39.7988 10.4168 39.3998C9.62156 39.0024 8.92637 38.4302 8.38307 37.7265C8.38123 37.7241 8.37941 37.7219 8.37757 37.7195L0.647131 27.7804C-0.394298 26.4414 -0.153086 24.5117 1.18589 23.4703C2.52488 22.4288 4.45459 22.6701 5.49602 24.009L13.0627 33.7377L37.561 4.09202C38.6415 2.78442 40.5778 2.60038 41.8853 3.68094Z"
          fill={props.color || '#3CD2A5'}
        />
      </g>
      <defs>
        <clipPath id="clip0_204_1830">
          <rect width="43" height="43" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
