import React from 'react';
import { twMerge } from 'tailwind-merge';

export function CloudWarning({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 73 73"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <g clip-path="url(#clip0_1136_10489)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M41.1075 0.24073C35.9357 -0.386157 30.7052 0.778914 26.2885 3.54166C22.1779 6.11285 19.0034 9.92314 17.2132 14.4089C14.9777 14.6555 12.8005 15.2922 10.7812 16.292C8.5152 17.4139 6.49242 18.9712 4.82829 20.8749C1.46743 24.7196 -0.228494 29.7419 0.113603 34.8369C0.455701 39.932 2.80779 44.6825 6.65244 48.0433C10.4744 51.3843 15.46 53.08 20.5241 52.764H26.073V37.228C26.073 31.4684 30.742 26.7994 36.5016 26.7994C42.2611 26.7994 46.9302 31.4684 46.9302 37.228V52.764H56.1746H56.1902C60.3121 52.739 64.2817 51.2039 67.3477 48.4489C70.4137 45.6938 72.3628 41.9101 72.8264 37.8144C73.2899 33.7185 72.2356 29.5949 69.8626 26.2244C67.6861 23.1323 64.5388 20.8708 60.9363 19.7885C60.2829 14.9742 58.1049 10.485 54.7088 6.98656C51.0799 3.24857 46.2793 0.86762 41.1075 0.24073ZM36.5016 33.3172C38.6614 33.3172 40.4123 35.0681 40.4123 37.228V55.478C40.4123 57.6378 38.6614 59.3887 36.5016 59.3887C34.3418 59.3887 32.5909 57.6378 32.5909 55.478V37.228C32.5909 35.0681 34.3418 33.3172 36.5016 33.3172ZM41.7159 67.7108C41.7159 70.5906 39.3813 72.925 36.5016 72.925C33.6218 72.925 31.2873 70.5906 31.2873 67.7108C31.2873 64.8309 33.6218 62.4965 36.5016 62.4965C39.3813 62.4965 41.7159 64.8309 41.7159 67.7108Z"
          fill="black"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M36.5016 33.3172C38.6614 33.3172 40.4123 35.0681 40.4123 37.228V55.478C40.4123 57.6378 38.6614 59.3887 36.5016 59.3887C34.3418 59.3887 32.5909 57.6378 32.5909 55.478V37.228C32.5909 35.0681 34.3418 33.3172 36.5016 33.3172ZM41.7159 67.7108C41.7159 70.5906 39.3813 72.925 36.5016 72.925C33.6218 72.925 31.2873 70.5906 31.2873 67.7108C31.2873 64.8309 33.6218 62.4965 36.5016 62.4965C39.3813 62.4965 41.7159 64.8309 41.7159 67.7108Z"
          fill="#E03E8C"
        />
      </g>
      <defs>
        <clipPath id="clip0_1136_10489">
          <rect width="73" height="73" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
