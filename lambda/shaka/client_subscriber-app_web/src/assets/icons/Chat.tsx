export function ChatIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="38"
      height="38"
      viewBox="0 0 38 38"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.9698 3.26359e-06C10.9996 3.26359e-06 7.19197 1.57717 4.38459 4.38454C1.57723 7.19192 7.44934e-05 10.9995 7.44934e-05 14.9697C-0.00927442 17.9164 0.861539 20.7986 2.5009 23.2471L0.500697 28.5998C0.279752 29.191 0.786429 29.7979 1.40765 29.6863L8.41833 28.425C9.1146 28.7643 9.83413 29.0485 10.5702 29.2754C10.3139 28.1533 10.1786 26.9853 10.1786 25.7856C10.1786 17.1661 17.1661 10.1785 25.7857 10.1785C26.9903 10.1785 28.1631 10.315 29.2892 10.5734C28.7401 8.78552 27.8596 7.11315 26.6904 5.64483C25.2873 3.88278 23.5046 2.46006 21.4753 1.4827C19.446 0.505334 17.2222 -0.00148394 14.9698 3.26359e-06ZM34.423 17.1489C32.1322 14.8583 29.0254 13.5714 25.7861 13.5714C23.9483 13.5702 22.1339 13.9837 20.4781 14.7812C18.8223 15.5786 17.3678 16.7395 16.223 18.1772C15.0781 19.6149 14.2725 21.2924 13.8661 23.0847C13.4598 24.8771 13.463 26.738 13.8758 28.529C14.2886 30.3198 15.1002 31.9946 16.2501 33.428C17.4 34.8616 18.8586 36.0174 20.5173 36.8089C22.1759 37.6004 23.9918 38.0075 25.8296 37.9999C27.6674 37.992 29.4798 37.5697 31.1317 36.7641L36.5928 37.7467C37.2141 37.8585 37.7206 37.2516 37.4996 36.6601L35.9598 32.5393C37.2974 30.5416 38.008 28.19 38.0004 25.7856C38.0004 22.5462 36.7136 19.4395 34.423 17.1489Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.9698 3.26359e-06C10.9996 3.26359e-06 7.19197 1.57717 4.38459 4.38454C1.57723 7.19192 7.44934e-05 10.9995 7.44934e-05 14.9697C-0.00927442 17.9164 0.861539 20.7986 2.5009 23.2471L0.500697 28.5998C0.279752 29.191 0.786429 29.7979 1.40765 29.6863L8.41833 28.425C9.1146 28.7643 9.83413 29.0485 10.5702 29.2754C10.3139 28.1533 10.1786 26.9853 10.1786 25.7856C10.1786 17.1661 17.1661 10.1785 25.7857 10.1785C26.9903 10.1785 28.1631 10.315 29.2892 10.5734C28.7401 8.78552 27.8596 7.11315 26.6904 5.64483C25.2873 3.88278 23.5046 2.46006 21.4753 1.4827C19.446 0.505334 17.2222 -0.00148394 14.9698 3.26359e-06ZM34.423 17.1489C32.1322 14.8583 29.0254 13.5714 25.7861 13.5714C23.9483 13.5702 22.1339 13.9837 20.4781 14.7812C18.8223 15.5786 17.3678 16.7395 16.223 18.1772C15.0781 19.6149 14.2725 21.2924 13.8661 23.0847C13.4598 24.8771 13.463 26.738 13.8758 28.529C14.2886 30.3198 15.1002 31.9946 16.2501 33.428C17.4 34.8616 18.8586 36.0174 20.5173 36.8089C22.1759 37.6004 23.9918 38.0075 25.8296 37.9999C27.6674 37.992 29.4798 37.5697 31.1317 36.7641L36.5928 37.7467C37.2141 37.8585 37.7206 37.2516 37.4996 36.6601L35.9598 32.5393C37.2974 30.5416 38.008 28.19 38.0004 25.7856C38.0004 22.5462 36.7136 19.4395 34.423 17.1489Z"
        fill="currentColor"
      />
    </svg>
  );
}
