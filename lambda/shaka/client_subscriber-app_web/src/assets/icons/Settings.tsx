import React from "react";

export function SettingsIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 52 52"
      xmlns="http://www.w3.org/2000/svg"
      className="w-5 h-5"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.57143 0C2.49442 1.34494e-07 -1.34502e-07 2.49441 0 5.57143L1.78592e-06 46.4286C1.92042e-06 49.5055 2.49442 52 5.57143 52H46.4286C49.5055 52 52 49.5055 52 46.4286V5.57143C52 2.49441 49.5055 1.62179e-06 46.4286 1.75629e-06L5.57143 0ZM14.3929 21.8585V40.8571C14.3929 42.1393 15.4322 43.1786 16.7143 43.1786C17.9963 43.1786 19.0357 42.1393 19.0357 40.8571V21.8585C21.4791 20.924 23.2143 18.5572 23.2143 15.7853C23.2143 12.1954 20.3041 9.28527 16.7143 9.28527C13.1244 9.28527 10.2143 12.1954 10.2143 15.7853C10.2143 18.5572 11.9495 20.924 14.3929 21.8585ZM41.7857 28.7854C41.7857 31.5574 40.0504 33.9241 37.6071 34.8586V40.8571C37.6071 42.1393 36.5678 43.1786 35.2857 43.1786C34.0036 43.1786 32.9643 42.1393 32.9643 40.8571V34.8586C30.5209 33.9241 28.7857 31.5574 28.7857 28.7854C28.7857 26.0134 30.5209 23.6467 32.9643 22.7122V11.1429C32.9643 9.86076 34.0036 8.82143 35.2857 8.82143C36.5678 8.82143 37.6071 9.86076 37.6071 11.1429V22.7122C40.0504 23.6467 41.7857 26.0134 41.7857 28.7854Z"
        fill="currentColor"
      />
    </svg>
  );
}

