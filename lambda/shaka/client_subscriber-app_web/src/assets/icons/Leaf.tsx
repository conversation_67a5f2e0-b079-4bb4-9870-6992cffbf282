import React from 'react';
import { twMerge } from 'tailwind-merge';

export function LeafIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 10 12"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <path
        d="M8.44829 4.45716C9.97741 6.64369 8.80763 9.14814 8.24185 10.1251C8.1589 10.2617 8.04743 10.3783 7.91539 10.4668C7.78327 10.5551 7.6338 10.6132 7.47729 10.6368C6.36866 10.8306 3.60859 11.0478 2.12534 8.8612C0.665031 6.79877 0.726196 3.17007 0.856172 1.44101C0.861294 1.31219 0.89651 1.18645 0.958929 1.07412C1.02135 0.961808 1.10917 0.866129 1.21518 0.794964C1.32119 0.723798 1.44234 0.679183 1.56867 0.664789C1.69501 0.650395 1.82291 0.666633 1.94185 0.712169C3.58565 1.1929 7.01089 2.3947 8.44829 4.45716Z"
        stroke="#1EC25F"
        strokeWidth="1.02153"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.29559 3.73639C5.04684 5.8293 6.58386 8.09722 7.88298 10.5053"
        stroke="#1EC25F"
        strokeWidth="1.02153"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
