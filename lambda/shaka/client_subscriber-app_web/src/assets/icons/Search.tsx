import React from 'react';
import { twMerge } from 'tailwind-merge';

export function SearchIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 7 8"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <path
        d="M2.98821 5.59862C4.29778 5.59862 5.35939 4.537 5.35939 3.22744C5.35939 1.91787 4.29778 0.856262 2.98821 0.856262C1.67865 0.856262 0.617037 1.91787 0.617037 3.22744C0.617037 4.537 1.67865 5.59862 2.98821 5.59862Z"
        stroke="currentColor"
        strokeWidth="0.9341"
      />
      <path
        d="M4.76893 5.00806L6.34972 6.58884"
        stroke="currentColor"
        strokeWidth="0.9341"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
