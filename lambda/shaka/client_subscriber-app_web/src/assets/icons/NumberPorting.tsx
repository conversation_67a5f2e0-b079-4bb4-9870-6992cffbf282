import React from 'react';
import { twMerge } from 'tailwind-merge';

export function NumberPortingIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 71 71"
      stroke="currentColor"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <path
        d="M68.2595 17.1447L68.4404 16.8408C69.1756 15.6057 69.5983 14.1627 69.5983 12.6196C69.5983 8.05625 65.8989 4.35689 61.3354 4.35693L58.6308 4.35696C54.8443 4.35699 51.6601 6.90127 50.6795 10.368C50.1829 12.1236 51.2038 13.9494 52.9591 14.446C54.7148 14.9426 56.5406 13.9218 57.037 12.1663L57.037 12.1661C57.2353 11.4662 57.8814 10.9641 58.6308 10.9641H61.3359C62.2501 10.9641 62.9912 11.7054 62.9912 12.6196C62.9912 13.5339 62.2501 14.2751 61.3359 14.2751H60.885C59.0601 14.2751 57.5815 15.7542 57.5815 17.5788C57.5815 19.4033 59.0606 20.8823 60.885 20.8823H61.3359C61.4215 20.8823 61.5067 20.881 61.592 20.8784L61.6112 20.8778L61.6303 20.8787C61.6821 20.8811 61.7342 20.8823 61.7867 20.8823C62.9499 20.8823 63.8929 21.8254 63.8929 22.9886V23.8903C63.8929 25.0536 62.9499 25.9966 61.7867 25.9966L58.1799 25.9967C57.2709 25.9967 56.4842 25.4179 56.1927 24.5928C55.5844 22.8725 53.6973 21.9709 51.9768 22.5788C50.2567 23.1868 49.3551 25.0742 49.963 26.7944C49.963 26.7944 49.963 26.7944 49.963 26.7945M68.2595 17.1447L68.87 17.0965C70.1997 18.6937 71 20.7478 71 22.9887V23.8903C71 28.9788 66.8749 33.1038 61.7867 33.1038L58.1799 33.1038C54.1624 33.1038 50.7549 30.5345 49.4916 26.9611L49.963 26.7945M68.2595 17.1447L68.4857 17.4164C69.7435 18.9271 70.5 20.869 70.5 22.9887V23.8903C70.5 28.7027 66.5988 32.6038 61.7867 32.6038L58.1799 32.6038C54.3813 32.6038 51.158 30.1748 49.963 26.7945M68.2595 17.1447L49.963 26.7945M14.2043 6.38187L14.2043 6.3819C14.3693 6.77452 14.4607 7.2062 14.4607 7.66057V25.4966V25.9966H14.9607H18.3702C20.1948 25.9966 21.6738 27.4757 21.6738 29.3002C21.6738 31.1248 20.1948 32.6038 18.3702 32.6038L11.1678 32.6038L11.1655 32.6038L11.1595 32.6039H11.1548L11.1489 32.6038H11.1465H3.94379C2.11927 32.6038 0.640219 31.1248 0.640219 29.3002C0.640219 27.4757 2.11928 25.9966 3.94379 25.9966H7.35357H7.85357V25.4966V16.7345V16.1389L7.26697 16.242C6.7738 16.3288 6.26602 16.374 5.74725 16.374H3.94394C2.11943 16.374 0.640365 14.895 0.640365 13.0705C0.640365 11.246 2.11943 9.7669 3.94394 9.7669H5.74725C6.91053 9.7669 7.85357 8.82386 7.85357 7.66057C7.85357 5.83602 9.33265 4.35697 11.1571 4.35697C12.5275 4.35697 13.7041 5.19142 14.2043 6.38187ZM31.7094 12.368L32.1588 12.5269L31.7094 12.3681C31.1014 14.0883 29.214 14.9899 27.4938 14.3819C25.7735 13.7739 24.8719 11.8865 25.4799 10.1662C26.6746 6.786 29.8981 4.35697 33.6967 4.35697H37.4437C42.1785 4.35697 46.0168 8.1953 46.0168 12.9301C46.0168 16.3362 44.0005 19.4193 40.8799 20.7845L32.8525 24.2965C32.3968 24.4958 32.037 24.8443 31.8202 25.2695L31.4493 25.9966H32.2656H42.7132C44.5377 25.9966 46.0168 27.4757 46.0168 29.3002C46.0168 31.1247 44.5377 32.6038 42.7132 32.6038H28.2868C26.4623 32.6038 24.9832 31.1247 24.9832 29.3002V26.2263C24.9832 22.7644 27.0326 19.6309 30.2042 18.2433L38.2317 14.7313C38.9473 14.4182 39.4097 13.7112 39.4097 12.9301C39.4097 11.8443 38.5295 10.9641 37.4437 10.9641H33.6967C32.7873 10.9641 32.001 11.543 31.7094 12.368ZM54.8502 41.8956L54.8502 41.8956C56.0844 41.3843 57.5053 41.6669 58.4502 42.6117L69.5323 53.694C70.8224 54.9842 70.8224 57.0755 69.5323 58.3656L58.4502 69.4477C57.5053 70.3926 56.0843 70.6753 54.8502 70.164L54.8502 70.164C53.6155 69.6526 52.8108 68.4479 52.8108 67.1119V61.1013V60.6013H52.3108H5.07143C2.54669 60.6013 0.5 58.5546 0.5 56.0298C0.5 53.505 2.54669 51.4584 5.07143 51.4584H52.3108H52.8108V50.9584V44.9477C52.8108 43.6115 53.6155 42.4069 54.8502 41.8956Z"
        fill="black"
        stroke="white"
      />
      <path
        d="M54.8502 41.8956L54.8502 41.8956C56.0844 41.3843 57.5053 41.6669 58.4502 42.6117L69.5323 53.694C70.8224 54.9842 70.8224 57.0755 69.5323 58.3656L58.4502 69.4477C57.5053 70.3926 56.0843 70.6753 54.8502 70.164L54.8502 70.164C53.6155 69.6526 52.8108 68.4479 52.8108 67.1119V61.1013V60.6013H52.3108H5.07143C2.54669 60.6013 0.5 58.5546 0.5 56.0298C0.5 53.505 2.54669 51.4584 5.07143 51.4584H52.3108H52.8108V50.9584V44.9477C52.8108 43.6115 53.6155 42.4069 54.8502 41.8956Z"
        fill="#E03E8C"
        stroke="white"
      />
    </svg>
  );
}
