import { createFileRoute, Link, Navigate } from '@tanstack/react-router';
import { useMemo, useState } from 'react';
import Container from 'src/components/common/Container';
import FullPageLoader from 'src/components/common/FullPageLoader';
import { ROUTES } from 'src/config/routes';
import usePlans from 'src/hooks/usePlans';
import PlanSlideCard from 'src/components/PlanSlideCard';
import ProgressLinks from 'src/components/common/ProgressLinks';
import useSubscription from 'src/hooks/useSubscription';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import { twMerge } from 'tailwind-merge';
import { useRequest } from 'src/hooks/useRequest';
import { updateSubscriber } from 'src/api/data';
import { getAwaitingPlanId } from 'src/config/localStorageActions';
import { isOpenId } from 'src/config/env-vars';

export const Route = createFileRoute('/_signup-flow/plan-confirmation')({
  component: PlanConfirmation
});

export function PlanConfirmation() {
  const { run: runUpdateSubscriber } = useRequest(updateSubscriber);
  const { plans, isLoading } = usePlans();
  const { subscriber, setSubscriber } = useSubscription();

  const [isOptIn, setIsOptIn] = useState(true);

  const selectedPlan = useMemo(() => {
    const planId = getAwaitingPlanId();
    const selectedPlan = plans.find(
      (plan) => plan.clientPlan.id === parseInt(planId!)
    );

    return selectedPlan;
  }, [plans]);

  const handleNextClick = () => {
    runUpdateSubscriber({ send_marketing: isOptIn }).then((data) => {
      setSubscriber(data);
    });
  };

  if (!selectedPlan && !isLoading) {
    <Navigate to={ROUTES.ExplorePlans} replace={true} />;
  }

  if (isLoading) {
    return (
      <Container>
        <FullPageLoader />
      </Container>
    );
  }

  return (
    <div className="mt-3">
      <PlanSlideCard clientPlan={selectedPlan?.clientPlan} />

      <div className="mt-8 mx-3 space-y-6">
        <div className="flex justify-between text-xs">
          <span className="font-semibold">Customer</span>
          <span className="text-gray-500">{subscriber?.name}</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="font-semibold">Duration</span>
          <span className="text-gray-500">monthly rolling subscription</span>
        </div>
        <div className="flex justify-between text-xs">
          <span className="font-semibold">Terms & conditions</span>
          <Link to={ROUTES.Terms} className="text-gray-500 underline">
            View T&Cs
          </Link>
        </div>
        {!isOpenId && (
          <div className="flex justify-between text-xs items-center">
            <span className="font-semibold">
              Opt-in for exclusive discounts & offers
            </span>
            <CheckRoundedFilledIcon
              className={twMerge(
                'size-5 md:hover:text-black/45 cursor-pointer transition-all',
                isOptIn ? 'text-black' : 'text-white'
              )}
              onClick={() => setIsOptIn(!isOptIn)}
            />
          </div>
        )}
      </div>

      <ProgressLinks
        backTo={ROUTES.ExplorePlans}
        nextTo={ROUTES.Checkout}
        onNextClick={handleNextClick}
      />
    </div>
  );
}
