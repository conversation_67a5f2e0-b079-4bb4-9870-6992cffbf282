import { useEffect } from 'react';
import { createFileRoute, Navigate, useNavigate } from '@tanstack/react-router';
import { LoginForm } from 'components/LoginForm';
import { ROUTES } from 'src/config/routes';
import { useAuth } from 'src/hooks/useAuth';
import { flushSync } from 'react-dom';
import { LoginData } from 'src/types/login';
import ProgressLinks from 'src/components/common/ProgressLinks';
import { clearStorage } from 'src/config/localStorageActions';
import { isOpenId } from 'src/config/env-vars';

function Login() {
  const auth = useAuth();
  const navigate = useNavigate();
  const {
    clientProps: { isSignupDisabled }
  } = Route.useRouteContext();

  const { redirect = ROUTES.Dashboard, back } = Route.useSearch();

  const handleLogin = (data: LoginData) => {
    clearStorage();
    flushSync(() => {
      auth.login(data);
    });
  };

  useEffect(() => {
    if (auth.isAuthenticated()) {
      navigate({ to: redirect });
    }
  }, [auth, navigate, redirect]);

  if (isOpenId) {
    return <Navigate to={ROUTES.OpenIdLogin} />;
  }

  return (
    <>
      <div className=" text-center font-semibold uppercase">Login</div>

      <div className="grow flex flex-col justify-end">
        <LoginForm onSubmit={handleLogin} disabledSignup={isSignupDisabled} />
      </div>

      {back && <ProgressLinks hideNextButton />}
    </>
  );
}

type RouteParams = {
  redirect?: string;
  back?: boolean;
};

export const Route = createFileRoute('/_signup-flow/login')({
  component: Login,
  validateSearch: (search: RouteParams): RouteParams => ({
    redirect: search?.redirect || ROUTES.Dashboard,
    back: search?.back || false
  })
});
