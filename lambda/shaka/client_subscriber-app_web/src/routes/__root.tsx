import { Outlet, createRootRouteWithContext } from '@tanstack/react-router';
import { AuthContextType } from '../context/AuthContext';
import useAnalytics from 'src/hooks/useAnalytics';
import useDisconnected from 'src/hooks/useDisconnected';
import { ToastContainer, Zoom } from 'react-toastify';

export const Route = createRootRouteWithContext<{
  auth: AuthContextType;
  clientProps: {
    isSignupDisabled: boolean;
  };
}>()({
  component: () => <Root />
});

function Root() {
  useAnalytics();
  useDisconnected();

  return (
    <>
      <Outlet />
      <ToastContainer
        position="top-center"
        autoClose={5000}
        closeOnClick
        rtl={false}
        draggable
        pauseOnHover
        theme="light"
        transition={Zoom}
      />
    </>
  );
}
