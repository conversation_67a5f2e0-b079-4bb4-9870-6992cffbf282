import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useEffect } from 'react';
import Container from 'src/components/common/Container';
import { ROUTES } from 'src/config/routes';

type RouteParams = {
  redirect?: string;
};

export const Route = createFileRoute('/connection-error')({
  component: ConnectionError,
  validateSearch: (search: RouteParams): RouteParams => ({
    redirect: search?.redirect || ROUTES.Dashboard
  })
});

function ConnectionError() {
  const { redirect } = Route.useSearch();
  const navigate = useNavigate();

  const handleOnline = () => {
    if (redirect) {
      console.log('redirected from connection error');
      navigate({ to: redirect, replace: true });
    }
  };

  useEffect(() => {
    if (navigator.onLine) {
      handleOnline();

      return;
    }

    window.addEventListener('online', handleOnline);

    return () => {
      window.removeEventListener('online', handleOnline);
    };
  }, []);

  return (
    <Container className="flex flex-col gap-6 justify-center text-center">
      <h1 className="text-2xl font-semibold mb-4">Connection Error</h1>
      <p className="text-gray-700">
        We are unable to connect to our servers. Please check your internet
        connection.
      </p>
    </Container>
  );
}
