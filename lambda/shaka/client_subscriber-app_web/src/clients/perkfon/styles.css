body {
  --primary-color: #000000;
  --secondary-color: #ffffff;
  --gray-color: #1a1a1a;
  --danger-color: #ff6969;
  --font-color: #000000;
  --secondary-font-color: #ffffff;
  --bg-color: #f3f3f3;
}

body {
  background: var(--bg-color);
  color: var(--font-color);
}

.text-primary {
  color: var(--primary-color);
}

.bg-primary {
  background: var(--primary-color);
}

/* Logo */
.perkfon .logo {
  width: 50%;
}

.perkfon .logo-wrapper.small .logo {
  width: 35%;
  height: 40px;
}

/* Initials */
.initials {
  background-color: var(--primary-color);
}

/* Link */
.button.primary,
.link-button.primary {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.button.secondary,
.link-button.secondary {
  background: var(--secondary-color);
}

/* Input */
.input {
  background: var(--secondary-color);
}

.input:focus,
.input:focus {
  outline-color: var(--primary-color);
}

/* Select */
.option.selected {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.select.focused {
  outline: 2px solid var(--primary-color);
}

/* Input validation mark */
.validation.valid {
  color: var(--primary-color);
}

/* Welcome page */
.perkfon .welcome-content {
  padding: 0 10%;
}

/* Contact phone */
.contact-phone {
  color: var(--primary-color);
}

/* Plan card */
.perkfon .plan-card-detailed {
  color: var(--font-color);
}

.perkfon .card-text,
.perkfon~div .card-text {
  color: #313131;
}

.card-used,
.card-icons {
  color: #313131;
}

.plan-card-logo .logo {
  margin: 0;
  width: 90px;
}

.perkfon .plan-card-title {
  font-weight: 400;
}

/* Offer  */
.offer-content {
  background: linear-gradient(173.3deg,
      #ffffff -0.66%,
      #eaeaea 11.96%,
      #e1e1e1 106.26%);
  border-radius: 20px;
  margin: 0 -4%;
  margin-top: 36px;
  padding: 5%;
}

.offer {
  background-color: #74cdc9;
  color: var(--secondary-font-color);
  border-radius: 20px;
  padding: 24px 20px 12px;
}

.otp input {
  outline-color: var(--primary-color);
}

/* sim activation */
.perkfon .sim-activation-logo {
  max-width: 40%;
}

/* Chip */
.perkfon .chip {
  background: rgba(0, 0, 0, 0.06);
}

/* terms */
.list-title {
  margin-bottom: 8px;
  margin-top: 16px;
  font-weight: 600;
}

.list-decimal {
  list-style-type: decimal;
  margin-left: 16px;
}

.big-list-margin li {
  margin-bottom: 16px;
}

.list-alpha {
  list-style-type: lower-alpha;
  margin-left: 16px;
}

.small-list-margin li {
  margin-bottom: 8px;
}

.list-roman {
  list-style-type: lower-roman;
  margin-left: 16px;
}

.list-disc {
  list-style-type: disc;
  margin-left: 16px;
}

.list-circle {
  list-style-type: circle;
  margin-left: 16px;
}

.paragraphs p {
  margin-bottom: 12px;
}

.paragraphs .bold {
  font-weight: 600;
}