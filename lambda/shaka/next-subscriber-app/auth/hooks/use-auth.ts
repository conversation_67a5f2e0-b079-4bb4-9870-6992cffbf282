'use client';

import { use } from 'react';
import { AuthContext, AuthContextValue } from '../context/auth-context';

export function useAuth(): AuthContextValue {
  const context = use(AuthContext);

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
}

export function useIsAuthenticated() {
  const { isAuthenticated, isLoading } = useAuth();
  return { isAuthenticated, isLoading };
}

// replace with subscriber ????
// export function useUser() {
//   const { user, isLoading } = useAuth();
//   return { user, isLoading };
// }

export function useLogin() {
  const { login, isLoading, error } = useAuth();
  return { login, isLoading, error };
}

export function useLogout() {
  const { logout, isLoading } = useAuth();
  return { logout, isLoading };
}

// export function useAuthMechanism(type: AuthMechanismType) {
//   const { getProviderByType, isLoading, error } = useAuth();
//   const provider = getProviderByType(type);
//
//   return {
//     provider,
//     isLoading,
//     error,
//     isConfigured: !!provider
//   };
// }
