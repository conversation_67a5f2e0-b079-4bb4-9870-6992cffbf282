'use client';

import React, { useState, useEffect } from 'react';
import { useCreateStripeSession } from '@/lib/stripe/useCreateStripeSession';
import { StripeProvider } from '@/lib/stripe/stripe-provider';
import { PaymentForm } from '@/src/uswitch/app/signup/_components/payment-form';
import { StripeMode } from '@/lib/stripe/types';
import Wrapper from '@/src/uswitch/app/signup/_components/wrapper/wrapper';
import { PlainCard } from '@/components/plain-card/plain-card';
import { useWizard } from '@/context/wizard/wizard';
import { usePlanSelection } from '@/src/uswitch/app/signup/hooks/use-plan-selection';
import { initialState } from '@/src/uswitch/app/signup/reducer/reducer';

const paymentMode: StripeMode = 'elements';

export default function PaymentPage() {
  const planSelectionState = usePlanSelection(initialState);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const { createSession, loading, error } = useCreateStripeSession();
  const { goToNextStep, goBackToPreviousStep } = useWizard();
  console.log(planSelectionState);  // tan stack query !!

  useEffect(() => {
    const initializePayment = async () => {
      try {
        const { clientSecret } = await createSession({
          mode: paymentMode
          // shouldnt it that be specified by backend ?
          // returnUrl: `${window.location.origin}/payment/return`
        });
        setClientSecret(clientSecret);
      } catch (err) {
        console.error('Failed to create payment session:', err);
      }
    };

    initializePayment();
  }, []);

  console.log(clientSecret);

  // replace with suspense ?
  if (loading) {
    return <div>Loading payment form...</div>;
  }

  if (error || !clientSecret) {
    return <div>Error: {error?.message}</div>;
  }

  return (
    <>
      <Wrapper>
        <PlainCard>
          <StripeProvider
            clientSecret={clientSecret}
            mode={paymentMode}
            options={{
              returnUrl: `${window.location.origin}/payment/return`
            }}
          >
            <PaymentForm clientSecret={clientSecret} />
          </StripeProvider>
        </PlainCard>
        <PlainCard className="bg-secondary top-8 order-1 block self-start lg:sticky lg:order-2">
          ASIDE 3
        </PlainCard>
      </Wrapper>
      <button onClick={goToNextStep}>Next</button>
      <button onClick={goBackToPreviousStep}>Back</button>
    </>
  );
}
