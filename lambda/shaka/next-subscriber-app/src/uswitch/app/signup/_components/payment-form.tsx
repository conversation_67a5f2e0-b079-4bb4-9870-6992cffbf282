'use client';

import React from 'react';
import {
  PaymentElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useStripeElements } from '@/lib/stripe/useStripeElements';
import { useRouter } from 'next/router';
import { API_ENDPOINTS } from '@/auth/api/endpoints';

type PaymentFormProps = {
  clientSecret: string;
};

export function PaymentForm({ clientSecret }: PaymentFormProps) {
  const router = useRouter();
  const stripe = useStripe();
  const elements = useElements();

  const { confirmPayment, loading, error } = useStripeElements({
    stripe,
    elements,
    clientSecret,
    // what is the point of passing onSuccess ?
    // onSuccess: (paymentIntent) => {
    //   // endpoints
    //   router.push('/payment/success');
    //   console.log('Payment succeeded:', paymentIntent);
    // },
    returnUrl: API_ENDPOINTS.stripe.successUrl('1'),
    onError: () => {
      router.push('/payment/error');
    }
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    confirmPayment();
  };

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      <button type="submit" disabled={loading}>
        {loading ? 'Processing...' : 'Pay now'}
      </button>
      {error && <div className="error">{error.message}</div>}
    </form>
  );
}
