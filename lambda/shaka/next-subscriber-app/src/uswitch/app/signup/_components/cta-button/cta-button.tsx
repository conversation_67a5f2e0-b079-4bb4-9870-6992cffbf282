import React from 'react';
import Link from 'next/link';

// TODO: add as prop to make it link
// prop with icon or just children

interface CtaButtonProps {
  text: string;
  href?: string | Record<string, any>;
  className?: string;
}

function CtaButton({ text, href, className }: CtaButtonProps) {
  return (
    <Link
      prefetch
      className={`text-bold w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold ${className || ''}`}
      href={href || '#'}
    >
      {text}
    </Link>
  );
}

export default CtaButton;
