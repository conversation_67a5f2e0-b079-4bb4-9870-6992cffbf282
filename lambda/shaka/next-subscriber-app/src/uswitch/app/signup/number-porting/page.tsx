'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Wrapper from '@/src/uswitch/app/signup/_components/wrapper/wrapper';
import {
  ConditionalWrapper,
  DesktopOnly
} from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { usePathname } from 'next/navigation';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';
import CtaButton from '@/src/uswitch/app/signup/_components/cta-button/cta-button';
import { OrderSummary } from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import {
  ChevronDown,
  CloseIcon,
  PACIcon,
  PlayButtonIcon,
  TextMessageIcon
} from '@/icons/icons';
import { FullDetailsButton } from '@/src/uswitch/app/signup/_components/full-details-button/full-details-button';
import Modal from '@/components/modal/modal';
import Image from 'next/image';
import PACiphone from 'public/images/PAC-iphone.png';
import { initialState } from '@/src/uswitch/app/signup/reducer/reducer';
import { shouldShowProgressBar } from '@/src/uswitch/utils/helpers';
import { usePlanSelection } from '@/src/uswitch/app/signup/hooks/use-plan-selection';
import Button from '@/src/uswitch/components/button/button';

export default function NumberPorting() {
  const pathname = usePathname();
  const showProgressBar = shouldShowProgressBar(pathname);
  const planSelectionState = usePlanSelection(initialState);

  return (
    <>
      <Wrapper>
        <div className="order-2 flex flex-col gap-4 lg:order-1">
          <ConditionalWrapper className="p-6">
            {showProgressBar && (
              <DesktopOnly>
                <ProgressBar withLabel ariaLabel="signup progress bar">
                  <ProgressBar.Track>
                    <ProgressBar.Bar className="bg-blueberry mb-6" />
                  </ProgressBar.Track>
                </ProgressBar>
              </DesktopOnly>
            )}
            <h1 className="mb-4 lg:mb-0">Want to keep an exisiting number?</h1>
            <Divider className="my-6 hidden lg:block" />
            <h2 className="mt-4 mb-2">How do I keep my number?</h2>
            <p>
              This is really easy to do and only takes a few minutes. You can
              also do this later so don’t worry if you need to skip this for
              now.
            </p>
            <PACInfoGrid />
            <Divider className="mt-8 mb-6" />
            <h2 className="mb-2">Enter your PAC code</h2>
            <p>This is really easy to do and only takes a few minutes.</p>
            <PACForm />
            {/*needs to trigger form submission !*/}
            {/*trigger validation when not in form*/}
            <Button
              form="pacForm"
              className="bg-primary hover:bg-primary-hover text-bold mt-6 hidden w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white lg:inline-block"
            >
              Keep your number
            </Button>
            <CtaButton
              className="bg-secondary text-primary border-primary hover:bg-secondary-hover mt-2 hidden border hover:text-white lg:inline-block"
              text="Skip for now"
              href={ROUTES_CONFIG['payment'].path}
            />
          </ConditionalWrapper>
        </div>
        <OrderSummary state={planSelectionState} />
      </Wrapper>
      <div className="px-6">
        <Button
          form="pacForm"
          className="bg-primary hover:bg-primary-hover text-bold mt-6 inline-block w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white lg:hidden"
        >
          Keep your number
        </Button>
        <CtaButton
          text="Skip for now"
          href={ROUTES_CONFIG['payment'].path}
          className="bg-secondary text-primary border-primary hover:bg-secondary-hover mt-2 inline-block border hover:text-white lg:hidden"
        />
      </div>
    </>
  );
}

export function PACInfoGrid() {
  return (
    <>
      <div className="mt-6 mb-4 grid gap-4 lg:grid-cols-2">
        <PACCard
          icon={<PACIcon />}
          title="Text “PAC” to 65075"
          description="This notifies your current network you would like to switch away from them and keep your number."
        />
        <div className="order-4 lg:order-none lg:row-span-2">
          <Image
            priority
            className="w-full lg:ml-auto lg:max-w-[340px]"
            src={PACiphone}
            alt="Number porting"
            width={293}
            height={227}
          />
        </div>
        <PACCard
          icon={<TextMessageIcon />}
          title="Enter your code below"
          description="You’ll receive your PAC code from your old network in a few minutes which you need to enter below."
        />
        <div className="order-3 lg:order-none">
          <PlayButtonIcon className="mr-2 inline align-middle" />
          <FullDetailsButton text="Watch an instructional video">
            {({ isOpen, setIsOpen }) =>
              isOpen && (
                <Modal onOpenChange={setIsOpen} open={isOpen}>
                  <Modal.Overlay />
                  <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
                    <div className="mb-4 flex justify-end">
                      <Modal.Close>
                        <CloseIcon />
                      </Modal.Close>
                    </div>
                    <Modal.Title className="mb-6 text-xl font-semibold">
                      <p>hi</p>
                    </Modal.Title>
                    <Modal.Description>
                      <p>hello</p>
                    </Modal.Description>
                  </Modal.Content>
                </Modal>
              )
            }
          </FullDetailsButton>
        </div>
      </div>
    </>
  );
}

interface PACProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function PACCard({ icon, title, description }: PACProps) {
  return (
    <div className="order-1 grid grid-cols-[minmax(0,0.1fr)_minmax(0,0.9fr)] gap-6 lg:order-none">
      {icon}
      <div>
        <h3 className="mb-2 text-[18px]">{title}</h3>
        <p className="text-balanced">{description}</p>
      </div>
    </div>
  );
}

function PACForm() {
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const data = Object.fromEntries(formData);
    console.log(data);
  };

  return (
    <form onSubmit={handleSubmit} id="pacForm" className="date-picker mt-4">
      <div>
        <label htmlFor="pacCode" className="font-semibold">
          Your PAC code
        </label>
        <input
          id="pacCode"
          name="pacCode"
          type="text"
          placeholder="PAC432564"
          className="placeholder-placeholder border-border placeholder:text-default mt-2 w-full rounded-[2px] border p-3"
        />
      </div>
      <div>
        <label htmlFor="phoneNumber" className="font-semibold">
          Your phone number
        </label>
        <input
          id="phoneNumber"
          name="phoneNumber"
          type="tel"
          inputMode="numeric"
          placeholder="07123456789"
          className="placeholder-placeholder border-border placeholder:text-default mt-2 w-full rounded-[2px] border p-3"
        />
      </div>
      <div className="col-span-2 xl:col-span-1">
        <label htmlFor="switchingDate" className="font-semibold">
          {/*- what is the default if skipped ?*/}
          Choose your switching date (optional)
        </label>
        <PacDeliveryDatePicker />
      </div>
    </form>
  );
}

function DOBSelect() {
  // if feb leap year !!
  const days = Array.from({ length: 31 }, (_, i) => i + 1);
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 10 }, (_, i) => currentYear + i);

  return (
    <div className="mt-2 grid grid-cols-3 gap-3">
      {/* Day */}
      <div className="relative">
        <select
          id="day"
          name="day"
          className="border-border text-placeholder w-full cursor-pointer appearance-none rounded-[2px] border p-2 xl:p-3"
        >
          <option value="" disabled>
            DD
          </option>
          {days.map((day) => (
            <option key={day} value={day.toString().padStart(2, '0')}>
              {day.toString().padStart(2, '0')}
            </option>
          ))}
        </select>
        <ChevronDown className="pointer-events-none absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform text-gray-400" />
      </div>
      {/* Month */}
      <div className="relative">
        <select
          id="month"
          name="month"
          // onChange={(e) => handleInputChange('month', e.target.value)}
          className="border-border text-placeholder w-full cursor-pointer appearance-none rounded-[2px] border p-2 xl:p-3"
        >
          <option value="" disabled>
            MM
          </option>
          {months.map((month, index) => (
            <option key={month} value={(index + 1).toString().padStart(2, '0')}>
              {(index + 1).toString().padStart(2, '0')}
            </option>
          ))}
        </select>
        <ChevronDown className="text-placeholder pointer-events-none absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform" />
      </div>
      {/* Year */}
      <div className="relative">
        <select
          id="year"
          name="year"
          // onChange={(e) => handleInputChange('year', e.target.value)}
          className="border-border text-placeholder w-full cursor-pointer appearance-none rounded-[2px] border p-2 xl:p-3"
        >
          <option value="" disabled>
            YY
          </option>
          {years.map((year) => (
            <option key={year} value={year.toString().slice(-2)}>
              {year.toString().slice(-2)}
            </option>
          ))}
        </select>
        <ChevronDown className="text-placeholder pointer-events-none absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform" />
      </div>
    </div>
  );
}

const isLeapYear = (year) => {
  const fullYear = year < 100 ? 2000 + parseInt(year) : parseInt(year);
  return (fullYear % 4 === 0 && fullYear % 100 !== 0) || fullYear % 400 === 0;
};

const getDaysPerMonth = (month: string, year: string) => {
  if (!month || !year) return 31;

  const monthNum = parseInt(month);
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

  if (monthNum === 2 && isLeapYear(year)) {
    return 29;
  }

  const daysInCurrentMonth = daysInMonth[monthNum - 1];

  return daysInCurrentMonth || 31;
};

const getDaysInMonth = (maxDays: number) => {
  return Array.from({ length: maxDays }, (_, i) => i + 1);
};

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December'
];

const getYearsRange = (
  yearRange = { start: 1900, end: new Date().getFullYear() + 10 }
) => {
  return Array.from(
    { length: yearRange.end - yearRange.start + 1 },
    (_, i) => yearRange.start + i
  ).reverse();
};

const baseSelectClasses = `
    w-full cursor-pointer appearance-none rounded-[2px] border p-2 xl:p-3
    bg-white text-placeholder rounded-[2px] border border-border
  `;

const iconClasses = `
    pointer-events-none absolute top-1/2 right-3 h-5 w-5 -translate-y-1/2 transform
    transition-colors duration-200
  `;

interface DatePickerProps {
  initialState?: { day: string; month: string; year: string };
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  className?: string;
}

const DatePicker = ({
  initialState = { day: '', month: '', year: '' },
  disabled = false,
  required = false,
  error = false,
  className = ''
}: DatePickerProps) => {
  const [selectedDate, setSelectedDate] = useState(initialState);
  const maxDays = getDaysPerMonth(selectedDate.month, selectedDate.year);
  const days = getDaysInMonth(maxDays);
  const years = getYearsRange();

  return (
    <div
      className={`mt-2 ${className}`}
      role="group"
      aria-labelledby="date-picker-label"
    >
      <div className="grid grid-cols-3 gap-3">
        {/* Day */}
        <div className="relative">
          <label htmlFor="day-select" className="sr-only">
            Day
          </label>
          <select
            id="day-select"
            name="day"
            value={selectedDate.day}
            onChange={(e) =>
              setSelectedDate({ ...selectedDate, day: e.target.value })
            }
            disabled={disabled}
            required={required}
            aria-label="Select day"
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
          >
            <option value="" disabled>
              DD
            </option>
            {days.map((day) => (
              <option key={day} value={day.toString().padStart(2, '0')}>
                {day.toString()}
              </option>
            ))}
          </select>
          <ChevronDown
            className={`${iconClasses} ${disabled ? 'text-gray-400' : 'text-red-600'}`}
            aria-hidden="true"
          />
        </div>

        {/* Month */}
        <div className="relative">
          <label htmlFor="month-select" className="sr-only">
            Month
          </label>
          <select
            id="month-select"
            name="month"
            value={selectedDate.month}
            onChange={(e) =>
              setSelectedDate({ ...selectedDate, month: e.target.value })
            }
            disabled={disabled}
            required={required}
            aria-label="Select month"
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
          >
            <option value="" disabled>
              MM
            </option>
            {months.map((month, index) => (
              <option
                key={month}
                value={(index + 1).toString().padStart(2, '0')}
              >
                {month}
              </option>
            ))}
          </select>
          <ChevronDown className={iconClasses} aria-hidden="true" />
        </div>

        {/* Year */}
        <div className="relative">
          <label htmlFor="year-select" className="sr-only">
            Year
          </label>
          <select
            id="year-select"
            name="year"
            value={selectedDate.year}
            onChange={(e) =>
              setSelectedDate({ ...selectedDate, year: e.target.value })
            }
            disabled={disabled}
            required={required}
            aria-label="Select year"
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
          >
            <option value="" disabled>
              YY
            </option>
            {years.map((year) => (
              <option key={year} value={year.toString().slice(-2)}>
                {year.toString()}
              </option>
            ))}
          </select>
          <ChevronDown className={iconClasses} aria-hidden="true" />
        </div>
      </div>

      {error && (
        <div id="date-error" className="mt-1 text-sm text-red-600" role="alert">
          Please select a valid date
        </div>
      )}
    </div>
  );
};

const PacDeliveryDatePicker = ({
  initialState = { day: '', month: '', year: '' },
  error = false,
  className = ''
}: Omit<DatePickerProps, 'disabled' | 'required'>) => {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth() + 1;
  const currentDay = today.getDate();

  // Generate array of months (current month only if current year is selected)
  const months = useMemo(() => {
    return [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ].map((month, index) => ({
      name: month,
      value: (index + 1).toString().padStart(2, '0'),
      disabled: false
    }));
  }, []);

  // Generate array of days based on selected month and year
  const getDaysInMonth = useCallback(
    (month: string, year: string) => {
      if (!month || !year) return [];

      const monthNum = parseInt(month, 10);
      const yearNum = parseInt(year, 10);
      const isCurrentYear = yearNum === currentYear;
      const isCurrentMonth = monthNum === currentMonth;

      // Get number of days in the month
      const daysInMonth = new Date(yearNum, monthNum, 0).getDate();

      // If it's the current month and year, only allow from today to today + 14
      if (isCurrentYear && isCurrentMonth) {
        const maxDay = Math.min(currentDay + 14, daysInMonth);
        return Array.from({ length: maxDay - currentDay + 1 }, (_, i) =>
          (currentDay + i).toString().padStart(2, '0')
        );
      }

      // For other months in current year, allow all days
      if (isCurrentYear) {
        return Array.from({ length: daysInMonth }, (_, i) =>
          (i + 1).toString().padStart(2, '0')
        );
      }

      return [];
    },
    [currentDay, currentMonth, currentYear]
  );

  const [selectedDate, setSelectedDate] = useState({
    ...initialState,
    year: currentYear.toString() // Always set to current year
  });

  // Update available days when month or year changes
  const days = useMemo(() => {
    return getDaysInMonth(selectedDate.month, selectedDate.year);
  }, [selectedDate.month, selectedDate.year, getDaysInMonth]);

  // Reset day if it's no longer valid
  useEffect(() => {
    if (selectedDate.day && !days.includes(selectedDate.day)) {
      setSelectedDate((prev) => ({
        ...prev,
        day: ''
      }));
    }
  }, [days, selectedDate.day]);

  return (
    <div
      className={`mt-2 ${className}`}
      role="group"
      aria-labelledby="date-picker-label"
    >
      <div className="grid grid-cols-3 gap-3">
        {/* Day */}
        <div className="relative">
          <label htmlFor="day-select" className="sr-only">
            Day
          </label>
          <select
            id="day-select"
            name="day"
            value={selectedDate.day}
            onChange={(e) =>
              setSelectedDate({ ...selectedDate, day: e.target.value })
            }
            disabled={!selectedDate.month}
            required
            aria-label="Select day"
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
          >
            <option value="" disabled>
              DD
            </option>
            {days.map((day) => (
              <option key={day} value={day}>
                {parseInt(day, 10)}
              </option>
            ))}
          </select>
          <ChevronDown className={iconClasses} aria-hidden="true" />
        </div>

        {/* Month */}
        <div className="relative">
          <label htmlFor="month-select" className="sr-only">
            Month
          </label>
          <select
            id="month-select"
            name="month"
            value={selectedDate.month}
            onChange={(e) =>
              setSelectedDate({
                ...selectedDate,
                month: e.target.value,
                day: '' // Reset day when month changes
              })
            }
            required
            aria-label="Select month"
            aria-describedby={error ? 'date-error' : undefined}
            className={baseSelectClasses}
          >
            <option value="" disabled>
              MM
            </option>
            {months.map((month) => (
              <option
                key={month.value}
                value={month.value}
                disabled={month.disabled}
              >
                {month.name}
              </option>
            ))}
          </select>
          <ChevronDown className={iconClasses} aria-hidden="true" />
        </div>

        {/* Year - Only current year */}
        <div className="relative">
          <label htmlFor="year-select" className="sr-only">
            Year
          </label>
          <select
            id="year-select"
            name="year"
            value={currentYear}
            disabled
            className={baseSelectClasses}
          >
            <option value={currentYear}>{currentYear}</option>
          </select>
          <ChevronDown className={iconClasses} aria-hidden="true" />
        </div>
      </div>

      {error && (
        <div id="date-error" className="mt-1 text-sm text-red-600" role="alert">
          Please select a valid date
        </div>
      )}
    </div>
  );
};
