// src/uswitch/hooks/usePlanSelectionState.ts
import { useSearchParams } from 'next/navigation';
import { paramsOptions } from '@/src/uswitch/utils/constants';
import type { PlanState } from '@/src/uswitch/utils/constants';

export function usePlanSelection(initialState: PlanState) {
  const searchParams = useSearchParams();

  const basicPlanQuantity = searchParams.get(paramsOptions.basic);
  const bigFamilyPlanQuantity = searchParams.get(paramsOptions.family);
  const selectedRoamingPackageId = searchParams.get(paramsOptions.roaming);

  return {
    basicPlanQuantity:
      Number(basicPlanQuantity) || initialState.basicPlanQuantity,
    bigFamilyPlanQuantity:
      Number(bigFamilyPlanQuantity) || initialState.bigFamilyPlanQuantity,
    selectedRoamingPackageId:
      Number(selectedRoamingPackageId) || initialState.selectedRoamingPackageId
  };
}
