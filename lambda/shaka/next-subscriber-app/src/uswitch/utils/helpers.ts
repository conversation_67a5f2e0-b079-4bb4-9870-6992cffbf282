import {
  GlobalRoamingPackage,
  globalRoamingPackages,
  OrderItemWithQuantity,
  planOrderItems,
  PlanState
} from '@/src/uswitch/utils/constants';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';

export function getRoamingPackageById(
  id: number
): GlobalRoamingPackage | undefined {
  return globalRoamingPackages.find((pkg) => pkg.id === id);
}

export function createRoamingOrderItem(
  selectedRoamingPackageId: number
): OrderItemWithQuantity {
  const selectedPackage = getRoamingPackageById(selectedRoamingPackageId);

  if (!selectedPackage) {
    throw new Error(
      `Roaming package with ID ${selectedRoamingPackageId} not found`
    );
  }

  return {
    id: 'selectedRoamingPackageId',
    name: 'Monthly global roaming',
    price: selectedPackage.price,
    quantity: 1,
    displayName: `${selectedPackage.data}GB Monthly global roaming`
  };
}

export function createOrderItemsWithQuantity(
  state: PlanState
): OrderItemWithQuantity[] {
  const planItems: OrderItemWithQuantity[] = planOrderItems.map((item) => ({
    ...item,
    quantity: state[item.id] as number,
    displayName: item.name
  }));

  const roamingItem = createRoamingOrderItem(state.selectedRoamingPackageId);

  return [...planItems, roamingItem];
}

export function calculateTotalCost(
  orderItems: OrderItemWithQuantity[]
): number {
  return orderItems.reduce(
    (total, item) => total + item.price * item.quantity,
    0
  );
}

export function createOrderSummary(state: PlanState) {
  const orderItems = createOrderItemsWithQuantity(state);
  const totalCost = calculateTotalCost(orderItems);

  return {
    orderItems,
    totalCost
  };
}

export const getInitialStateFromUrl = (
  searchParams: URLSearchParams,
  initialState: PlanState
) => {
  return {
    basicPlanQuantity: Number(
      searchParams.get('basic') || initialState.basicPlanQuantity
    ),
    bigFamilyPlanQuantity: Number(
      searchParams.get('family') || initialState.bigFamilyPlanQuantity
    ),
    selectedRoamingPackageId: Number(
      searchParams.get('roaming') || initialState.selectedRoamingPackageId
    )
  };
};

export const shouldShowProgressBar = (pathname: string): boolean => {
  return (
    pathname !== ROUTES_CONFIG['successful-payment'].path &&
    pathname !== ROUTES_CONFIG['payment-error'].path
  );
};
