import React from 'react';

interface ButtonProps extends React.PropsWithChildren {
  className?: string;
  form?: string;
}

function Button({ children, className, ...props }: ButtonProps) {
  return (
    <button
      form="pacForm"
      className={`bg-primary hover:bg-primary-hover text-bold mt-6 hidden w-full cursor-pointer rounded-[2px] p-3 text-center font-semibold text-white ${className || ''}`}
      {...props}
    >
      {children}
    </button>
  );
}

export default Button;
