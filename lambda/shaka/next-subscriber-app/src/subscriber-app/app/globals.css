@import "tailwindcss";
/*important !!! pull all higher level components that use tailwind classes here !*/
@import "../clients/active/client.css";
@source "../../../components";
/*@source "../clients/active/client.css";*/

/*Ideas for variables and classes according to the prototype*/

/*Variables:*/
/*1. card-border-radius*/
/*2. card-padding*/
/*3. card-shadow*/

/*Colors:*/
/*1. white*/
/*2. gray*/
/*3. black  */
/*4. conic grdient for card ?
/*5. red*/
/*6. orange*/

/*Classes:*/
/*1. link ( blue ish, underline )*/
/*2. card ( border radius, padding, shadow)*/

/* Example of usage*/
/*TODO: double check docs for syntax in tailwind 4*/

/*@theme {*/
/*    --card-radius: 0.75rem;*/
/*    --card-padding: 1.5rem;*/
/*    --card-shadow:*/
/*            0 10px 15px -3px rgba(0,0,0,0.1),*/
/*            0 4px 6px -4px rgba(0,0,0,0.1);*/
/*    --card-bg: #ffffff;*/
/*}*/

/*TODO: find better name*/
@layer components {
    .card {
        @apply rounded-[var(--card-radius)] p-[var(--card-padding)] shadow-[var(--card-shadow)] bg-[var(--card-bg)];
    }
}
