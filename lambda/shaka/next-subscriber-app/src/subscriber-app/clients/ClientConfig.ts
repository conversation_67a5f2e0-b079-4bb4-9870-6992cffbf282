/**
 * ClientConfig interface defines the contract for per-client configuration, branding, and theming.
 *
 * How this works:
 * - Each client (e.g. clientA, clientB) has its own folder in ./clients/ with a client-config.tsx, client.css, and related components.
 * - At build time, the build script copies the selected client folder to ./clients/active/.
 * - The app always imports config and components from './clients/active/client-config'.
 * - For CSS, Tailwind v4 exposes theme tokens as CSS variables (e.g., --color-primary), which you override per client in ./clients/[client]/client.css.
 * - Tailwind utility classes (e.g. bg-primary, text-primary) will use the value of these variables, so per-client theming is automatic.
 * - Shared Tailwind base and utilities (and any global custom CSS) live in app/globals.css.
 *
 * To add a new client:
 * 1. Create a new folder under ./clients/ (e.g., ./clients/clientC/).
 * 2. Implement client-config.tsx, client.css (with @theme or :root CSS variable overrides), Policy.tsx, Terms.tsx, etc., conforming to this interface.
 * 3. Add a build script or run the build script with the new client key.
 *
 * Usage in code:
 *   import { clientConfig } from '../clients/active/client-config';
 *   // Tailwind utility classes like bg-primary, text-primary, bg-bg, etc. will use per-client values.
 *
 * The build script ensures the correct client config and theming is always used for the current build.
 */
import { ReactNode } from 'react';

// EXAMPLE INTERFACE - MODIFY AS NEEDED logo etc
export interface ClientConfig {
  policy: ReactNode;
  terms: ReactNode;
  backgroundImageNumber: number;
  plainDashboardCard: boolean;
  supportEmail: string;
  supportLink: string;
  help_esim_install: string;
  help_data_issue: string;
  // Per-client HTML head fields:
  // ADD OGG META TAGS
  title: string;
  metaDescription: string;
  faviconHref: string;
}
