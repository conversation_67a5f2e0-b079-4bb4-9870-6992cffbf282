import React from 'react';

// TODO - make more customisable
export function CoverageIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_93_16620)">
        <path
          d="M7.27158 4.17502C5.52291 5.50283 4.38462 7.66879 4.38462 10.1172C4.38462 12.5656 5.52291 14.7316 7.27158 16.0594L6.45919 17.2344C4.36446 15.6438 3 13.0478 3 10.1172C3 7.18659 4.36446 4.59056 6.45919 3L7.27158 4.17502Z"
          fill="#434343"
        />
        <path
          d="M19.6154 10.1172C19.6154 7.66879 18.4771 5.50283 16.7284 4.17502L17.5408 3C19.6355 4.59056 21 7.18659 21 10.1172C21 13.0478 19.6355 15.6438 17.5408 17.2344L16.7284 16.0594C18.4771 14.7316 19.6154 12.5656 19.6154 10.1172Z"
          fill="#434343"
        />
        <path
          d="M7.15385 10.1172C7.15385 8.90561 7.73824 7.80488 8.6883 7.07462L7.86939 5.90456C6.60364 6.87748 5.76923 8.39389 5.76923 10.1172C5.76923 11.8405 6.60364 13.3569 7.86939 14.3298L8.6883 13.1598C7.73824 12.4295 7.15385 11.3288 7.15385 10.1172Z"
          fill="#434343"
        />
        <path
          d="M15.3117 7.07462C16.2618 7.80488 16.8462 8.90561 16.8462 10.1172C16.8462 11.3288 16.2618 12.4295 15.3117 13.1598L16.1306 14.3298C17.3964 13.3569 18.2308 11.8405 18.2308 10.1172C18.2308 8.39389 17.3964 6.87748 16.1306 5.90456L15.3117 7.07462Z"
          fill="#434343"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12 7.21512C10.4706 7.21512 9.23077 8.51442 9.23077 10.1172C9.23077 11.4694 10.1133 12.6057 11.3077 12.9278L11.3077 21H12.6923L12.6923 12.9278C13.8867 12.6057 14.7692 11.4694 14.7692 10.1172C14.7692 8.51442 13.5294 7.21512 12 7.21512ZM10.6154 10.1172C10.6154 9.31581 11.2353 8.66616 12 8.66616C12.7647 8.66616 13.3846 9.31581 13.3846 10.1172C13.3846 10.9186 12.7647 11.5682 12 11.5682C11.2353 11.5682 10.6154 10.9186 10.6154 10.1172Z"
          fill="#434343"
        />
      </g>
      <defs>
        <clipPath id="clip0_93_16620">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function PlusIcon() {
  return (
    <svg
      aria-hidden="true"
      width="14"
      height="3"
      viewBox="0 0 11 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="11" height="1" fill="white" />
    </svg>
  );
}

export function MinusIcon() {
  return (
    <svg
      aria-hidden="true"
      width="16"
      height="16"
      viewBox="0 0 11 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M6 5.5H11V6.5H6V11.5H5V6.5H0V5.5H5V0.5H6V5.5Z" fill="white" />
    </svg>
  );
}

export function CloseIcon() {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.205 18.0249L25.59 27.4099L27.4099 25.59L18.0249 16.205L27.41 6.81992L25.5901 5L16.205 14.3851L6.81992 5L5 6.81992L14.3851 16.205L5.00006 25.59L6.81998 27.4099L16.205 18.0249Z"
        fill="#141414"
      />
    </svg>
  );
}

export function TickIcon() {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.18468 14.0036L7.18342 14.0048L8.12643 14.9478L17.1313 5.94301L16.1882 5L8.12769 13.0606L4.06801 9.00088L3.125 9.94389L7.18468 14.0036Z"
        fill="#141414"
      />
    </svg>
  );
}

export function ChevronDown({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 16.8105L3.96966 8.78022L5.03032 7.71956L12 14.6892L18.9697 7.71956L20.0303 8.78022L12 16.8105Z"
        fill="#141414"
      />
    </svg>
  );
}

export function ChevronUp() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 7.18945L20.0303 15.2198L18.9697 16.2804L12 9.31077L5.03033 16.2804L3.96967 15.2198L12 7.18945Z"
        fill="#141414"
      />
    </svg>
  );
}

export function PlayButtonIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.375 10.8333L7.5 14.375V6.25L14.375 10.8333ZM8.75 12.3249V8.58565L11.914 10.695L8.75 12.3249Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 1.25C5.16751 1.25 1.25 5.16751 1.25 10C1.25 14.8325 5.16751 18.75 10 18.75C14.8325 18.75 18.75 14.8325 18.75 10C18.75 5.16751 14.8325 1.25 10 1.25ZM2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10C17.5 14.1421 14.1421 17.5 10 17.5C5.85786 17.5 2.5 14.1421 2.5 10Z"
        fill="#141414"
      />
    </svg>
  );
}

export function PACIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="40" height="40" rx="2" fill="#E5F3FF" />
      <path d="M26 17L14 17V15L26 15V17Z" fill="#141414" />
      <path d="M14 21L26 21V19L14 19V21Z" fill="#141414" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 8H33V28H25.4142L20 33.4142L14.5858 28H7V8ZM9 10V26H15.4142L20 30.5858L24.5858 26H31V10H9Z"
        fill="#141414"
      />
    </svg>
  );
}
export function TextMessageIcon() {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="40" height="40" rx="2" fill="#E5F3FF" />
      <path d="M24 7H7V33H24V26H22V31H9V9H24V7Z" fill="#141414" />
      <path d="M17 29H14V27H17V29Z" fill="#141414" />
      <path d="M22 17L29 17V15L22 15V17Z" fill="#141414" />
      <path d="M22 20V18H26V20H22Z" fill="#141414" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 11V27L21.6 24H33V11H18ZM31 22H20.8759L20 22.7299V13H31V22Z"
        fill="#141414"
      />
    </svg>
  );
}
