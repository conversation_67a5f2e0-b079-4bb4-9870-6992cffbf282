import React, { createContext, use } from 'react';
import { MinusIcon, PlusIcon } from '@/icons/icons';

interface QuantityInputContextValue {
  value: number;
  minQuantity: number;
  maxQuantity: number;
  onIncrement: () => void;
  onDecrement: () => void;
  canIncrement: boolean;
  canDecrement: boolean;
}

const QuantityInputContext = createContext<QuantityInputContextValue | null>(
  null
);

function useQuantityInputContext() {
  const context = use(QuantityInputContext);
  if (!context) {
    throw new Error(
      'QuantityInput compound components must be used within QuantityInput.Root'
    );
  }
  return context;
}

interface QuantityInputRootProps {
  value: number;
  onIncrement: () => void;
  onDecrement: () => void;
  minQuantity?: number;
  maxQuantity?: number;
  children: React.ReactNode;
  className?: string;
}

function QuantityInputRoot({
  value,
  onIncrement,
  onDecrement,
  minQuantity = 0,
  maxQuantity = Infinity,
  children,
  className = 'flex items-center gap-4'
}: QuantityInputRootProps) {
  const canIncrement = value < maxQuantity;
  const canDecrement = value > minQuantity;

  const contextValue: QuantityInputContextValue = {
    value,
    minQuantity,
    maxQuantity,
    onIncrement,
    onDecrement,
    canIncrement,
    canDecrement
  };

  return (
    <QuantityInputContext.Provider value={contextValue}>
      <div className={className}>{children}</div>
    </QuantityInputContext.Provider>
  );
}

interface QuantityInputButtonProps {
  action: 'increment' | 'decrement';
  children?: React.ReactNode;
  className?: string;
  'aria-label'?: string;
}

function QuantityInputButton({
  action,
  children,
  className = 'bg-primary text-secondary flex h-8 w-8 cursor-pointer items-center justify-center rounded-full outline disabled:cursor-not-allowed disabled:opacity-20',
  'aria-label': ariaLabel
}: QuantityInputButtonProps) {
  const { onIncrement, onDecrement, canIncrement, canDecrement } =
    useQuantityInputContext();

  const isIncrement = action === 'increment';
  const handler = isIncrement ? onIncrement : onDecrement;
  const isDisabled = isIncrement ? !canIncrement : !canDecrement;
  const defaultAriaLabel = isIncrement
    ? 'Increase quantity'
    : 'Decrease quantity';
  const defaultIcon = !isIncrement ? <PlusIcon /> : <MinusIcon />;

  return (
    <button
      onClick={handler}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      aria-label={ariaLabel || defaultAriaLabel}
      className={className}
    >
      {children || defaultIcon}
    </button>
  );
}

interface QuantityInputDisplayProps {
  className?: string;
  format?: (value: number) => string;
}

function QuantityInputDisplay({
  className = 'border-border flex h-8 w-8 items-center justify-center rounded border font-semibold',
  format = (value) => value.toString()
}: QuantityInputDisplayProps) {
  const { value } = useQuantityInputContext();

  return <output className={className}>{format(value)}</output>;
}

export const QuantityInput = {
  Root: QuantityInputRoot,
  Button: QuantityInputButton,
  Display: QuantityInputDisplay
};
