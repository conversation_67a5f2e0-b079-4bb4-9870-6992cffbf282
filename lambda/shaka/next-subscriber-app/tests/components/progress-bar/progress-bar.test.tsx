import { vi } from 'vitest';
vi.mock('@/context/wizard/wizard', () => ({
  useWizard: () => ({
    currentStepNumber: 2,
    totalSteps: 4
  })
}));

import { describe, it, expect } from 'vitest';
import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { ProgressBar } from '@/components/progress-bar/progress-bar';
import { customRender } from '@/tests/utils/custom-render';

describe('ProgressBar', () => {
  it('renders with default props and correct ARIA attributes', () => {
    customRender(<ProgressBar ariaLabel="Signup Progress" />);

    const progressbars = screen.getAllByRole('progressbar', {
      name: /signup progress/i
    });

    const progressbar = progressbars[0];
    expect(progressbar).toBeInTheDocument();

    expect(progressbar).toHaveAttribute('aria-valuenow', '2');
    expect(progressbar).toHaveAttribute('aria-valuemin', '0');
    expect(progressbar).toHaveAttribute('aria-valuemax', '4');
  });

  it('renders correct step text for screen readers', () => {
    customRender(<ProgressBar />);
    expect(screen.getByText(/step 2 of 4/i)).toBeInTheDocument();
  });

  it('renders children', () => {
    customRender(
      <ProgressBar>
        <div data-testid="child">Child</div>
      </ProgressBar>
    );
    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('renders hidden native <progress> element for accessibility', () => {
    customRender(<ProgressBar ariaLabel="Signup Progress" />);
    const progressBars = screen.getAllByRole('progressbar', {
      name: /signup progress/i
    });

    const progress = progressBars[0];

    expect(progress.querySelector('progress')).toBeInTheDocument();
  });
});

describe('ProgressBar.Bar', () => {
  it('renders with correct width for mid progress', async () => {
    customRender(
      <ProgressBar>
        <ProgressBar.Bar />
      </ProgressBar>
    );

    await waitFor(() => {
      const progressbarContainer = screen.getAllByRole('progressbar')[0];
      const bar = progressbarContainer.querySelector('.bg-gray-800');

      expect(bar).toHaveStyle({ width: '50%' });
    });
  });

  it('renders 0% width for step 0', async () => {
    customRender(
      <ProgressBar>
        <ProgressBar.Bar />
      </ProgressBar>
    );

    await waitFor(() => {
      const progressbarContainer = screen.getAllByRole('progressbar')[0];
      const bar = progressbarContainer.querySelector('.bg-gray-800');

      expect(bar).toHaveStyle({ width: '0%' });
    });
  });

  it('renders 100% width for last step', async () => {
    vi.resetModules();

    vi.doMock('@/context/wizard/wizard', () => ({
      useWizard: () => ({
        currentStepNumber: 4,
        totalSteps: 4
      })
    }));

    // Re-import ProgressBar after the mock ( mocks are hoisted in Vitest so in order to make it work we need to re-import it )
    const { ProgressBar } = await import(
      '@/components/progress-bar/progress-bar'
    );

    customRender(
      <ProgressBar>
        <ProgressBar.Bar />
      </ProgressBar>
    );

    await waitFor(() => {
      const progressbarContainer = screen.getAllByRole('progressbar')[0];
      const bar = progressbarContainer.querySelector('.bg-gray-800');

      expect(bar).toHaveStyle({ width: '100%' });
    });
  });

  it('throws error if used outside ProgressBar', () => {
    const spy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => customRender(<ProgressBar.Bar />)).toThrow(
      'ProgressBar.Bar must be used within ProgressBar'
    );
    spy.mockRestore();
  });
});

describe('ProgressBar.Track', () => {
  it('renders children inside track', () => {
    customRender(
      <ProgressBar>
        <ProgressBar.Track>
          <div data-testid="track-child">Track Child</div>
        </ProgressBar.Track>
      </ProgressBar>
    );
    expect(screen.getByTestId('track-child')).toBeInTheDocument();
  });
});
