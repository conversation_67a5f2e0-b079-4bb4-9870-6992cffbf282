import * as yup from "yup";

const dateErrorMessage = "Please enter a valid date.";

export const subscriberName = yup
  .string()
  .required("Name is required")
  .min(2, "Name should be at least 2 characters long.")
  .matches(
    /^[a-zA-Z\s.'-]{2,} [a-zA-Z\s.'-]{2,}$/,
    "We need your full name"
  )
  .matches(
    /^[a-zA-Z\s.'-]{2,}$/,
    "Name shouldn't contain any numbers or other symbols."
  )
  .transform((value) => value.trim());

export const passwordScheme = yup
  .string()
  .required("No password provided.")
  .matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/,
    "Your password isn’t safe enough"
  );

export const emailScheme = yup
  .string()
  .email("Email is invalid")
  .required("No email provided.");

export const dobScheme = yup
  .string()
  .required("No date provided.")
  .min(10, dateErrorMessage)
  .test("is a valid date", dateErrorMessage, (value) => {
    console.log('value: ', value);
    if (!value) return false;

    const [year, month, day] = value!.split("-").map((v) => parseInt(v));
    const date = new Date(year, month - 1, day);

    if (isNaN(date.getTime()) || date.getMonth() != month - 1) {
      return false;
    }

    return true;
  });
