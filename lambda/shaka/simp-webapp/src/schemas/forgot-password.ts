import * as yup from "yup";
import { passwordScheme } from "./common";

export type ForgotPasswordInputs = {
  email: string;
};

export const ForgotPasswordSchema = yup.object({
  email: yup.string().email("Email is invalid").required("No email provided."),
});

export type ConfirmForgotPasswordInputs = {
  verification_code: string;
  email: string;
  new_password: string;
  new_password_confirmation: string;
};

export const ConfirmForgotPasswordSchema = yup.object({
  verification_code: yup.string().required("No verification code provided."),
  email: yup.string().email("Email is invalid").required("No email provided."),
  new_password: passwordScheme,
  new_password_confirmation: yup
    .string()
    .when("new_password", (new_password, schema) => {
      return schema.test({
        test: (confirmation) =>
          Boolean(new_password) &&
          Bo<PERSON>an(confirmation) &&
          confirmation === new_password[0],
        message: "Passwords must match",
      });
    }),
});
