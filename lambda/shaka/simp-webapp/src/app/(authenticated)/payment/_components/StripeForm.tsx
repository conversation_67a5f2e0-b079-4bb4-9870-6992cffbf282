import { Button } from "@/components/Button";
import ErrorText from "@/components/ErrorText";
import { PaymentElement, useCheckout } from "@stripe/react-stripe-js";
import { useState } from "react";

type ConfirmError = {
  message: string;
};

export default function StripeForm() {
  const { confirm } = useCheckout();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ConfirmError | null>(null);

  const handleSubmit = (event: { preventDefault: () => void }) => {
    event.preventDefault();
    setLoading(true);
    confirm().then((result) => {
      if (result.type === "error") {
        setError(result.error);
      }
      setLoading(false);
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      <div className="mt-6">
        {error && (
          <div className="mb-4">
            <ErrorText>{error.message}</ErrorText>
          </div>
        )}
        <Button variant="filled" type="submit" loading={loading}>
          Submit Payment
        </Button>
      </div>
    </form>
  );
}
