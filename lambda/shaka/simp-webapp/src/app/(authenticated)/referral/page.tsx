"use client";

import SignUpContent from "@/components/SignUpContent";
import ReferralForm from "./_components/ReferralForm";
import withAuth from "@/hoc/withAuth";
import { useSubscriber } from "@/hooks/useSubscriber";
import Loading from "@/components/Loading";

function ReferralPage() {
  const { subscriber } = useSubscriber();

  return (
    <SignUpContent stepIndex={4} biggerRight>
      <SignUpContent.Left
        title="Referral"
        description="Use your referral code now. You will not be able to use it later."
      />
      <SignUpContent.Right>
        <div className="md:mt-[120px]">
          <div className="md:p-8 md:bg-[#d9d9d91a] rounded-2xl w-full md:h-[376px] flex flex-col md:justify-center">
          {subscriber ? <ReferralForm /> : <Loading />}
        </div>
        </div>
      </SignUpContent.Right>
    </SignUpContent>
  );
}

export default withAuth(ReferralPage);
