import { Navigation } from "@/app/navigation";
import { Button } from "@/components/Button";
import Dialog from "@/components/Dialog";
import { useAuth } from "@/context/AuthContext";
import useLocalStorage, { LocalKey } from "@/hooks/useLocalStorage";
import { useEffect, useState } from "react";
import { resendVerificationCode } from "@/api/auth";
import Image from "next/image";

export default function StartAgainDialog() {
  const { logout } = useAuth();
  const { setLSValue } = useLocalStorage(LocalKey.REDIRECT);
  const [countdown, setCountdown] = useState(0);

  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleStartAgain = () => {
    setLSValue(Navigation.SIGN_UP);
    logout();
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  const onCodeResend = () => {
    resendVerificationCode().then(() => {
      setCountdown(60);
    });
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => prevCountdown - 1);
      }, 1000);
    }

    return () => {
      clearInterval(timer);
    };
  }, [countdown]);

  return (
    <>
      <Button
        variant="outlined"
        onClick={() => setIsPopupOpen(true)}
        type="button"
      >
        Code not received?
      </Button>

      <Dialog
        title={
          <div className="flex gap-2 items-center relative md:pr-20">
            Don’t ESCape just yet
            <Image
              src="/images/escape.png"
              alt="Escape room"
              width={108}
              height={70}
              className="md:absolute -top-5 right-4 max-md:w-[30%]"
            />
          </div>
        }
        onClose={closePopup}
        open={isPopupOpen}
        autoWidth
      >
        <div className="max-md:text-[14px] text-white md:w-[440px] flex flex-col align-center mt-6 md:mt-10">
          <div className="space-y-3">
            <span>
              {countdown > 0
                ? `Code sent! Try again in ${countdown} seconds`
                : "No code received?"}
            </span>
            <Button
              variant="filled-gray"
              type="button"
              disabled={countdown > 0}
              onClick={onCodeResend}
            >
              Re-send code
            </Button>
          </div>

          <div className="flex flex-col gap-3 mt-7">
            <span className="max-md:text-[14px]">Did you type your email address wrong?</span>
            <Button
              variant="filled-gray"
              type="button"
              onClick={handleStartAgain}
            >
              Start sign-up process again
            </Button>
          </div>
        </div>
      </Dialog>
    </>
  );
}
