import Image from "next/image";
import Link from "next/link";
import { Navigation } from "./navigation";
import { Button } from "@/components/Button";

export default function NotFound() {
  return (
    <div className="fixed inset-0 flex justify-center items-center flex-col pointer-events-none">
      <Image src="/images/not-found.png" alt="404" width={228} height={228} className="max-md:w-[50%]" />
      <h2 className="text-3xl font-bold md:text-[60px] md:mt-14">Hmmm...</h2>
      <p className="mt-5 text-xl md:text-3xl w-[70%] max-w-[530px] text-center">Looks like this page doesn’t exist. Sorry about that!</p>
      <div className="pointer-events-auto mt-14 text-center">
        <Link href={Navigation.HOME} className="inline-block w-[300px]"
        >
          <Button variant="filled">Go to homepage</Button>
        </Link>
      </div>
    </div>
  );
}
