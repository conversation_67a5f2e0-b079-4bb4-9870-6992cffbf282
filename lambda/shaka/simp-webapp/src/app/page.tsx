import Image from "next/image";
import SignUpContent from "@/components/SignUpContent";
import AppStoreButtons from "@/components/AppStoreButtons";
import LandingButtons from "./_components/LandingButtons";
import BottomButtons from "./_components/BottomButtons";
import FeaturesList from "@/components/FeaturesList";

export default function Home() {
  return (
    <SignUpContent biggerRight withBackButton={false}>
      <SignUpContent.Left
        title={<span className="font-bold">SIM, perfected.</span>}
        description="The eSIM mobile network that loves you a little too much. Rewards, prizes and more."
        descriptionClassName="text-[20px] leading-6 max-md:max-w-[250px] mx-auto text-white"
        actionButton={
          <div className="flex gap-10 flex-col md:gap-20 w-full">
            <LandingButtons />
          </div>
        }
        hideGoBack
        keepButtonOnMobile
        fixedActionButtons={false}
      />
      <SignUpContent.Right>
        <div className="justify-end hidden md:flex md:mt-10">
          <Image
            src="/images/phone.png"
            alt="floating phone"
            width={700}
            height={600}
          />
        </div>
        <Image
          src="/images/phone-mobile.png"
          alt="phone"
          width={700}
          height={600}
          className="md:w-1/2 w-full max-w-sm m-auto block md:hidden"
          loading="lazy"
        />
        <div className="md:hidden mb-12">
          <FeaturesList />
        </div>
        <BottomButtons />
        <div className="flex justify-center md:hidden">
          <AppStoreButtons withBorder noWrap size="medium" />
        </div>
      </SignUpContent.Right>
    </SignUpContent>
  );
}
