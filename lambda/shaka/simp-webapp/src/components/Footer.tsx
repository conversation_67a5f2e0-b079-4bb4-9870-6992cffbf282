import { Navigation } from "@/app/navigation";
import Image from "next/image";
import Link from "next/link";
import { twMerge } from "tailwind-merge";
import AppStoreButtons from "./AppStoreButtons";

const currentDate = new Date().getFullYear();

export default function Footer({
  paddingBottom,
  withSocialButtons,
}: {
  paddingBottom?: number;
  withSocialButtons?: boolean;
}) {
  return (
    <footer
      className={twMerge(
        "py-8 text-[#868686] flex flex-col-reverse lg:flex-row justify-between items-center md:items-center gap-y-6",
        paddingBottom === 100 && "max-md:pb-[100px]",
        paddingBottom === 160 && "max-md:pb-[160px]"
      )}
    >
      <div className="flex flex-col gap-3 md:flex-row lg:gap-10 items-center text-xs text-center">
        <p>&copy; Simp Industries Ltd, {currentDate}</p>
        <Link
          href={Navigation.TERMS}
          className="hover:text-white transition-colors"
        >
          Terms & Conditions - General
        </Link>
        <Link
          href={Navigation.TERMS_SPINS}
          className="hover:text-white transition-colors"
        >
          Terms & Conditions - Spins
        </Link>
        <Link
          href={Navigation.POLICY}
          className="hover:text-white transition-colors"
        >
          Privacy Policy
        </Link>
        <Link
          href={Navigation.COOKIES}
          className="hover:text-white transition-colors"
        >
          Cookie Policy
        </Link>
      </div>

      <div className="flex gap-2 mb-4 md:mb-0 items-center">
        {withSocialButtons && (
          <div className="hidden md:block mr-4">
            <AppStoreButtons withBorder size="small" />
          </div>
        )}
        <a
          href="https://www.tiktok.com/"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-white transition-colors"
        >
          <Image src="/icons/tiktok.svg" alt="tiktok" width={15} height={17} />
        </a>
        <a
          href="https://instagram.com"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:text-white transition-colors"
        >
          <Image
            src="/icons/insta.svg"
            alt="instagram"
            width={17}
            height={17}
          />
        </a>
      </div>
    </footer>
  );
}
