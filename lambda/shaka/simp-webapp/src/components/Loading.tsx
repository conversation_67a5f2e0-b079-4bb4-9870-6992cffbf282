import Image from "next/image";
import { twMerge } from "tailwind-merge";

export default function Loading({ fullPage }: { fullPage?: boolean }) {
  return (
    <div
      className={twMerge(
        "flex justify-center items-center h-full",
        fullPage && "grow"
      )}
    >
      <div className="flex flex-col items-center">
        <div className="mb-4">
          <Image
            src="/images/spinner.svg"
            width={42}
            height={42}
            alt="Loading"
          />
        </div>
        <p className="text-sm text-white/50">Loading...</p>
      </div>
    </div>
  );
}
