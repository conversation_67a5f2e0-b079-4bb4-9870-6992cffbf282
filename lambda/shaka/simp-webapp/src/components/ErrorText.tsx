import Image from "next/image";
import { twMerge } from "tailwind-merge";

export type ErrorContent = string | string[] | undefined;

export default function ErrorText({
  children,
  size = "lg",
  absolute,
}: {
  children: ErrorContent;
  size?: "sm" | "lg";
  absolute?: boolean;
}) {
  if (!children) return null;

  const isArray = Array.isArray(children);

  return (
    <div
      className={twMerge(
        "text-[#FF5A1E] max-md:mt-3",
        size === "lg" ? "text-base" : "text-xs leading-3",
        absolute ? "md:absolute top-[calc(100%_+_10px)]" : "mt-1"
      )}
    >
      {isArray ? (
        <ul>
          {children.map((child, index) => (
            <li key={index} className="flex items-center gap-2">
              <Image
                src="/icons/error.svg"
                width={16}
                height={16}
                alt="Error"
              />
              <span className="mt-1">{child}</span>
            </li>
          ))}
        </ul>
      ) : (
        <span className="flex items-center gap-2">
          <Image src="/icons/error.svg" width={16} height={16} alt="Error" />{" "}
          <span className="mt-1">{children}</span>
        </span>
      )}
    </div>
  );
}
