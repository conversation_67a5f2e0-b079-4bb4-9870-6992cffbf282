import axios from "axios";

const googleApi = axios.create({
  baseURL: "https://www.googleapis.com/oauth2/v3/userinfo",
});

const facebookApi = axios.create({
  baseURL: "https://graph.facebook.com/me?fields=email,name",
});

export const getGoogleUserData = async (accessToken: string) => {
  const response = await googleApi.get("", {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  return response.data;
};

export const getFacebookUserData = async (accessToken: string) => {
  const response = await facebookApi.get("", {
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });

  return response.data;
};