import {
  LoginData,
  SignupPayload,
  LoginSsoPayload,
  Tokens,
  TokensResponse,
} from "@/types/auth";
import { Subscriber } from "@/types/subscriber";
import api from ".";

const client_id = process.env.NEXT_PUBLIC_CLIENT_ID;

export const login = (payload: LoginData) =>
  api.post("/auth/login/", payload).then((res) => res.data);

export const signUp = async (payload: SignupPayload): Promise<TokensResponse> => {
  const { data } = await api.post("/auth/sign-up/", payload);
  return data;
};

export const fetchSubscriber = (): Promise<Subscriber> =>
  api.get(`/data/subscriber/`).then((res) => res.data);

export const logout = async () => {
  await api.post("/auth/logout");
};

export const sendForgotPassword = (payload: { email: string }) =>
  api.post("/auth/forgot-password/", payload).then((res) => res.data);

export const sendConfirmForgotPassword = (payload: {
  verification_code: string;
  email: string;
  new_password: string;
}) =>
  api.post("/auth/confirm-forgot-password/", payload).then((res) => res.data);

export const loginSso = (payload: LoginSsoPayload): Promise<Tokens> =>
  api
    .post("/auth/login/sso/", { ...payload, client_id })
    .then((res) => res.data);

export const verify = (payload: { code: string }) =>
  api.post("/auth/verify/", payload).then((res) => res.data);

export const resendVerificationCode = () =>
  api.post('/auth/verify/resend/').then((res) => res.data);
