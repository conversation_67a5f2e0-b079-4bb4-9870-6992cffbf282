"use client";
import { sendConfirmForgotPassword, sendForgotPassword } from "@/api/auth";
import {
  ConfirmForgotPasswordInputs,
  ConfirmForgotPasswordSchema,
  ForgotPasswordInputs,
  ForgotPasswordSchema,
} from "@/schemas/forgot-password";
import { FormState } from "@/types/form-state";
import { parseYupError } from "./parse-yup-error";
import { redirect } from "next/navigation";

type Error = {
  response: {
    data: {
      error: string;
    };
  };
};

export async function forgotPassword(
  state: FormState<object, ForgotPasswordInputs>,
  formData: FormData
): Promise<FormState<object, ForgotPasswordInputs>> {
  const formFields = {
    email: formData.get("email")?.toString(),
  };

  try {
    await ForgotPasswordSchema.validate(formFields, { abortEarly: false });

    const response = await sendForgotPassword({
      email: formFields.email!,
    });

    const { error } = response;

    if (error) {
      return { formError: [error] };
    }
  } catch (err) {
    return parseYupError<ForgotPasswordInputs>(err);
  }

  const encodedEmail = encodeURIComponent(formFields.email!);
  redirect(`/forgot-password/confirmation?email=${encodedEmail}`);
}

export async function confirmForgotPassword(
  state: FormState<object, ConfirmForgotPasswordInputs>,
  formData: FormData
): Promise<FormState<object, ConfirmForgotPasswordInputs>> {
  const formFields = {
    new_password: formData.get("new_password")?.toString(),
    new_password_confirmation: formData
      .get("new_password_confirmation")
      ?.toString(),
    email: formData.get("email")?.toString(),
    verification_code: formData.get("verification_code")?.toString(),
  };

  try {
    await ConfirmForgotPasswordSchema.validate(formFields, {
      abortEarly: false,
    });

    const response = await sendConfirmForgotPassword({
      email: formFields.email!,
      new_password: formFields.new_password!,
      verification_code: formFields.verification_code!,
    }).catch((e) => {
      const error = e as Error;
      const errorMessage =
        error.response?.data.error ||
        "Something went wrong. Please try again later";

      return { error: errorMessage };
    });

    const { error } = response;

    if (error) {
      return { formError: [error] };
    }
  } catch (err) {
    return parseYupError<ConfirmForgotPasswordInputs>(err);
  }

  redirect("/login");
}
