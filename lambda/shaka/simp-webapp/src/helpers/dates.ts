export const epochToDate = (epoch: number) => new Date(epoch * 1000);

const getDayWithSuffix = (day: number) => {
  if (day > 3 && day < 21) return `${day}th`;
  switch (day % 10) {
    case 1:
      return `${day}st`;
    case 2:
      return `${day}nd`;
    case 3:
      return `${day}rd`;
    default:
      return `${day}th`;
  }
};

export const getHumanReadableDateFormat = (date: Date) => {
  const day = date.getDate();
  const month = date.toLocaleString("default", { month: "long" });
  const year = date.getFullYear();

  const dayWithSuffix = getDayWithSuffix(day);

  return `${dayWithSuffix} ${month} ${year}`;
};
