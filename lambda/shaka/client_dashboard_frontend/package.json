{"name": "client_dashboard_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "tsc": "tsc --noEmit", "preview": "vite preview", "test": "vitest", "format": "prettier --write src/**/*.{ts,tsx} --config .prettierrc.json"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write", "git add ."], "*.{ts,tsx}": ["bash -c tsc --noEmit"]}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^3.3.4", "@tanstack/react-table": "^8.11.0", "@uiw/react-color": "^2.0.5", "axios": "^1.6.2", "clsx": "^2.0.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "luxon": "^3.4.4", "path": "^0.12.7", "react": "^18.2.0", "react-charts": "^3.0.0-beta.57", "react-csv-downloader": "^3.0.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-ga4": "^2.1.0", "react-hook-form": "^7.49.2", "react-modern-drawer": "^1.2.2", "react-router-dom": "^6.21.0", "react-slider": "^2.0.6", "react-spinners": "^0.13.8", "react18-input-otp": "^1.1.4", "swiper": "^11.0.5", "tailwind-merge": "^2.4.0", "url": "^0.11.3", "yup": "^1.3.3", "zustand": "^4.5.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/lodash": "^4.14.202", "@types/luxon": "^3.3.7", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-slider": "^1.3.6", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "lefthook": "^1.7.12", "lint-staged": "^15.2.9", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-eslint": "^1.8.1", "vitest": "^1.0.4"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}