# Client dashboard

## Install dependencies

Run `yarn` to install dependencies

## How to configure environment

- Create an env file (`env.development.local` for the development, `.env.production.local` for the production build) in the root folder and copy contents from `.env.example`.
- Update `VITE_SHAKA_CLIENT_ID` in the corresponding env file as needed.

## Scripts

- `yarn dev`: Run the development server
- `yarn lint`: Check lint errors based on eslint configuration (.eslintrc.json)
- `yarn format`: Format code based on prettier configuration (.prettierrc.json)
- `yarn test`: Run tests
- `yarn build`: Run production build. The result is saved in _src/dist_ folder.
