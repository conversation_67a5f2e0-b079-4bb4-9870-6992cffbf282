type Props = {
  className?: string;
};

export default function ShareGradientIcon({ className = '' }: Props) {
  return (
    <svg
      width="19"
      height="21"
      viewBox="0 0 19 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M15.5371 13.6378C14.634 13.6375 13.7668 13.9911 13.1214 14.6227L6.83026 11.0856C6.95953 10.5521 6.95953 9.99547 6.83026 9.46198L13.1194 5.92485C13.707 6.49636 14.4788 6.84009 15.2966 6.89451C16.1145 6.94892 16.925 6.71045 17.5831 6.22181C18.2412 5.73317 18.7038 5.02625 18.8883 4.22761C19.0727 3.42897 18.9669 2.59075 18.5897 1.86303C18.2125 1.1353 17.5886 0.565581 16.8297 0.25586C16.0709 -0.0538615 15.2265 -0.0833605 14.4478 0.172644C13.6692 0.428648 13.0071 0.95344 12.58 1.65307C12.153 2.3527 11.9889 3.18148 12.1172 3.99105L5.67958 7.61228C5.17392 7.19099 4.55868 6.92252 3.90593 6.83833C3.25318 6.75413 2.58996 6.85769 1.99395 7.13688C1.39795 7.41608 0.893843 7.85933 0.5407 8.41472C0.187558 8.97012 0 9.61465 0 10.2728C0 10.931 0.187558 11.5755 0.5407 12.1309C0.893843 12.6863 1.39795 13.1295 1.99395 13.4087C2.58996 13.6879 3.25318 13.7915 3.90593 13.7073C4.55868 13.6231 5.17392 13.3546 5.67958 12.9333L12.1172 16.5565C12.0059 17.2559 12.1117 17.9726 12.4204 18.61C12.729 19.2474 13.2257 19.7748 13.8434 20.1212C14.4611 20.4676 15.1701 20.6162 15.875 20.5471C16.5798 20.478 17.2464 20.1945 17.7851 19.7348C18.3238 19.275 18.7086 18.6612 18.8876 17.976C19.0666 17.2908 19.0312 16.5672 18.7862 15.9027C18.5412 15.2382 18.0983 14.6649 17.5174 14.2599C16.9364 13.8549 16.2452 13.6378 15.5371 13.6378Z"
        fill="black"
        fillOpacity="0.21"
      />
      <path
        d="M15.5371 13.6378C14.634 13.6375 13.7668 13.9911 13.1214 14.6227L6.83026 11.0856C6.95953 10.5521 6.95953 9.99547 6.83026 9.46198L13.1194 5.92485C13.707 6.49636 14.4788 6.84009 15.2966 6.89451C16.1145 6.94892 16.925 6.71045 17.5831 6.22181C18.2412 5.73317 18.7038 5.02625 18.8883 4.22761C19.0727 3.42897 18.9669 2.59075 18.5897 1.86303C18.2125 1.1353 17.5886 0.565581 16.8297 0.25586C16.0709 -0.0538615 15.2265 -0.0833605 14.4478 0.172644C13.6692 0.428648 13.0071 0.95344 12.58 1.65307C12.153 2.3527 11.9889 3.18148 12.1172 3.99105L5.67958 7.61228C5.17392 7.19099 4.55868 6.92252 3.90593 6.83833C3.25318 6.75413 2.58996 6.85769 1.99395 7.13688C1.39795 7.41608 0.893843 7.85933 0.5407 8.41472C0.187558 8.97012 0 9.61465 0 10.2728C0 10.931 0.187558 11.5755 0.5407 12.1309C0.893843 12.6863 1.39795 13.1295 1.99395 13.4087C2.58996 13.6879 3.25318 13.7915 3.90593 13.7073C4.55868 13.6231 5.17392 13.3546 5.67958 12.9333L12.1172 16.5565C12.0059 17.2559 12.1117 17.9726 12.4204 18.61C12.729 19.2474 13.2257 19.7748 13.8434 20.1212C14.4611 20.4676 15.1701 20.6162 15.875 20.5471C16.5798 20.478 17.2464 20.1945 17.7851 19.7348C18.3238 19.275 18.7086 18.6612 18.8876 17.976C19.0666 17.2908 19.0312 16.5672 18.7862 15.9027C18.5412 15.2382 18.0983 14.6649 17.5174 14.2599C16.9364 13.8549 16.2452 13.6378 15.5371 13.6378Z"
        fill="url(#paint0_linear_816_340)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_816_340"
          x1="1.30625"
          y1="16.7079"
          x2="17.7387"
          y2="3.9417"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C468CC" />
          <stop offset="0.515804" stopColor="#607BB5" />
          <stop offset="1" stopColor="#744A96" />
        </linearGradient>
      </defs>
    </svg>
  );
}
