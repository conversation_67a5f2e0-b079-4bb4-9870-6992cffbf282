type Props = {
  fill?: string;
};

export default function BarChartIcon({ fill = '#9E9EA8' }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="19"
      height="19"
      viewBox="0 0 19 19"
      fill="none"
    >
      <path
        d="M3.85938 18.4062H2.67188C2.43567 18.4062 2.20913 18.3124 2.04211 18.1454C1.87508 17.9784 1.78125 17.7518 1.78125 17.5156V12.1719C1.78125 11.9357 1.87508 11.7091 2.04211 11.5421C2.20913 11.3751 2.43567 11.2812 2.67188 11.2812H3.85938C4.09558 11.2812 4.32212 11.3751 4.48914 11.5421C4.65617 11.7091 4.75 11.9357 4.75 12.1719V17.5156C4.75 17.7518 4.65617 17.9784 4.48914 18.1454C4.32212 18.3124 4.09558 18.4062 3.85938 18.4062Z"
        fill={fill}
      />
      <path
        d="M12.1719 18.4062H10.9844C10.7482 18.4062 10.5216 18.3124 10.3546 18.1454C10.1876 17.9784 10.0938 17.7518 10.0938 17.5156V8.60938C10.0938 8.37317 10.1876 8.14663 10.3546 7.97961C10.5216 7.81258 10.7482 7.71875 10.9844 7.71875H12.1719C12.4081 7.71875 12.6346 7.81258 12.8016 7.97961C12.9687 8.14663 13.0625 8.37317 13.0625 8.60938V17.5156C13.0625 17.7518 12.9687 17.9784 12.8016 18.1454C12.6346 18.3124 12.4081 18.4062 12.1719 18.4062Z"
        fill={fill}
      />
      <path
        d="M16.3281 18.4062H15.1406C14.9044 18.4062 14.6779 18.3124 14.5109 18.1454C14.3438 17.9784 14.25 17.7518 14.25 17.5156V4.45312C14.25 4.21692 14.3438 3.99038 14.5109 3.82336C14.6779 3.65633 14.9044 3.5625 15.1406 3.5625H16.3281C16.5643 3.5625 16.7909 3.65633 16.9579 3.82336C17.1249 3.99038 17.2188 4.21692 17.2188 4.45312V17.5156C17.2188 17.7518 17.1249 17.9784 16.9579 18.1454C16.7909 18.3124 16.5643 18.4062 16.3281 18.4062Z"
        fill={fill}
      />
      <path
        d="M8.01562 18.4062H6.82812C6.59192 18.4062 6.36538 18.3124 6.19836 18.1454C6.03133 17.9784 5.9375 17.7518 5.9375 17.5156V1.48438C5.9375 1.24817 6.03133 1.02163 6.19836 0.854608C6.36538 0.687583 6.59192 0.59375 6.82812 0.59375H8.01562C8.25183 0.59375 8.47837 0.687583 8.64539 0.854608C8.81242 1.02163 8.90625 1.24817 8.90625 1.48438V17.5156C8.90625 17.7518 8.81242 17.9784 8.64539 18.1454C8.47837 18.3124 8.25183 18.4062 8.01562 18.4062Z"
        fill={fill}
      />
    </svg>
  );
}
