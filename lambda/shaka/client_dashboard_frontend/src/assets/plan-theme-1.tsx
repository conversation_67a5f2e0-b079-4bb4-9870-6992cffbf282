export const PlanTheme1 = ({ className = '' }: { className?: string }) => {
  return (
    <svg
      viewBox="0 0 561 268"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M0 28.8192C0 13.1649 12.7038 0.474609 28.3581 0.474609C157.55 0.474609 253.82 0.474609 361.386 0.474609C421.718 0.474609 380.153 51.4118 439.133 51.4118H551.731L560.846 159.452V267.493H0V28.8192Z"
        fill="url(#paint0_linear_129_723)"
      />
      <path
        d="M551.731 51.4118L560.846 159.452V88.9445V69.1058V58.9704C560.846 54.7959 557.462 51.4118 553.287 51.4118H551.731Z"
        fill="url(#paint1_linear_129_723)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_129_723"
          x1="49.3287"
          y1="148.997"
          x2="508.3"
          y2="148.997"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2E215C" />
          <stop offset="1" stopColor="#5A7AB2" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_129_723"
          x1="49.3287"
          y1="148.997"
          x2="508.3"
          y2="148.997"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2E215C" />
          <stop offset="1" stopColor="#5A7AB2" />
        </linearGradient>
      </defs>
    </svg>
  );
};
