export enum BeamStatus {
  TO_SEND = 'to_send',
  SENDING = 'sending',
  SENT = 'sent',
  ERRORED = 'errored'
}

export type DeliveryReport = {
  delivery_rate: number;
  send_date: string;
  sender: string;
  sms_message: number;
  total_delivered: number;
  total_failed: number;
  total_pending: number;
  total_sent: number;
  total_units: number;
  units: number;
};

export type Beam = {
  title: string;
  message: string;
  send_on?: string;
  status: BeamStatus;
  id: number;
  delivery_report: DeliveryReport | null;
};
