export type Eligibility =
  | 'tenure'
  | 'total_spend'
  | 'total_points_earned'
  | 'airdrop'
  | 'no_free';

export type DiscountDetails = {
  discount_type: 'flat' | 'percentage';
  discount_amount: string | null;
  discount_percentage: string | null;
  discount_duration_months: number;
};

export type BoltOnDetails = {
  bolt_on: number[];
};

export type VoucherDetails = {
  merchant_name: string;
  code: string;
  url?: string;
  instructions?: string;
  expiry_date?: string;
};

export type Perk = {
  allow_multiple_redemptions: boolean;
  availability_date: string | null;
  cost_base?: number;
  description: string;
  discount_details?: {
    plan_discount: DiscountDetails;
  };
  bolt_on_details?: BoltOnDetails;
  voucher_details?: VoucherDetails;
  elective_redemption_cost: string;
  electively_redeemable: boolean;
  eligibility_threshold: string;
  eligibility_type: Eligibility;
  enabled: boolean;
  id?: number;
  name: string;
  perk_image: string | null;
  redemption_limit: number | null;
  target_group: string;
  redemptions_amount: number;
  remaining_quantity: number;
  total_cost: string;
  featured: boolean;
};

export type PerkHistory = {
  period: 'day';
  perk_id: number;
  date: string;
  redemptions: number;
  cost: number;
  quantity: number;
  is_unlimited: boolean;
};

export type PerkSubscriber = {
  user: {
    full_name: string;
    image: string;
    id: number;
  };
  redemption_date: string;
  quantity: number;
  is_unlimited: boolean;
};
