export enum PlanStatus {
  Inactive = 'inactive',
  Active = 'active'
}

export type BundleId = number;

export type Plan = {
  bundle_id: BundleId | null;
  voice_component_offering: number;
  sms_component_offering: number;
  data_component_offering: number;
  id: number;
  name: string;
  price: string;
  status: PlanStatus;
  created: string;
  implementation_datetime: string;
  plan_key: string;
  version_number: number;
  custom_voice_limit: number | null;
  custom_sms_limit: number | null;
  custom_data_limit: number | null;
  is_bundled: boolean;
  provider: number;
  points_per_month?: number | null;
};

export type Plans = Plan[];

export enum PlanDimension {
  Sms = 'sms',
  Voice = 'voice',
  Data = 'data'
}

type PlanComponent = {
  id: number;
  description: string;
  dimension: PlanDimension;
  bundle_only: boolean;
  allow_custom_limit: boolean;
  max_limit: number;
  provider: number;
};

export type PlanComponentOffering = {
  id: number;
  plan_component: PlanComponent;
  available_for_new_plans: boolean;
  price: number;
};

export type BundledPlan = {
  id: BundleId;
  description: string;
  is_available: boolean;
  price: number;
  sms: PlanComponentOffering;
  data: PlanComponentOffering;
  voice: PlanComponentOffering;
};

export type PlanOfferingSet = {
  bundled_plan_offerings: BundledPlan[];
  plan_component_offerings: PlanComponentOffering[];
};
