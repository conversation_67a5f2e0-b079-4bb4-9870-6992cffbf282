import { Branding } from 'src/contexts/BrandingContext';
import api from './index';
import { BrandingFormData } from 'src/views/customize/Branding/constants';

export type BrandingPayload = BrandingFormData & { client: number };

export const fetchBranding = (url: string): Promise<Branding> =>
  api.get(url).then((res) => res.data);

export const createBranding = (payload: BrandingPayload) =>
  api.post('/clientbrandings/', payload).then((res) => res.data);

export const updateBranding = (url: string, payload: BrandingPayload) =>
  api.put(url, payload).then((res) => res.data);
