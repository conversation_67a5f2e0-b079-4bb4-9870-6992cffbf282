import { LoginData } from 'src/types/auth';
import api from './index';

const clientID = import.meta.env.VITE_SHAKA_CLIENT_ID;

export const login = (payload: {
  email: string;
  password: string;
}): Promise<LoginData> =>
  api
    .post('/auth/login/', { id: clientID, ...payload })
    .then((res) => res.data);

export const sendForgotPassword = (payload: { email: string }) =>
  api.post('/auth/forgot-password/', { id: clientID, ...payload });

export const confirmForgotPassword = (payload: {
  verification_code: string;
  email: string;
  new_password: string;
}): Promise<LoginData> =>
  api
    .post('/auth/confirm-forgot-password/', { id: clientID, ...payload })
    .then((res) => res.data);

export const changePassword = (payload: {
  old_password: string;
  new_password: string;
}) => api.post('/auth/change-password/', payload).then((res) => res.data);
