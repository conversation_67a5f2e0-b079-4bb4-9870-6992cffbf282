import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';

export default function useImageDrop(onDrop?: (src: string) => void) {
  const [isDropped, setDropped] = useState(false);
  const [image, setImage] = useState('');
  const [error, setError] = useState('');
  const [metadata, setMetadata] = useState({
    name: '',
    width: 0,
    height: 0
  });

  const handleDrop = useCallback((acceptedFiles: File[]) => {
    setError('');
    const reader = new FileReader();
    reader.onload = () => {
      const image = new Image();
      image.onload = () => {
        if (acceptedFiles[0].size > 1000000) {
          // limit of 1MB
          setError('Maximum image size is 1MB. Please upload a smaller image.');
          return;
        }

        setImage(reader.result as string);
        onDrop?.(reader.result as string);

        setMetadata({
          name: acceptedFiles[0].name,
          width: image.width,
          height: image.height
        });
        setDropped(true);
      };
      image.src = URL.createObjectURL(acceptedFiles[0]);
    };
    reader.readAsDataURL(acceptedFiles[0]);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: handleDrop,
    accept: {
      'image/*': ['.png', '.gif', '.jpeg', '.jpg']
    },
    multiple: false
  });

  return {
    image,
    isDropped,
    metadata,
    error,
    getRootProps,
    getInputProps
  };
}
