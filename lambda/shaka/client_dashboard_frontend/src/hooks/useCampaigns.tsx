import { useEffect } from 'react';
import { fetchCampaigns } from 'src/api/campaign';
import { useAsync } from 'src/api/useApi';

import {
  selectorCampaignById,
  selectorCampaignsLoaded,
  setCampaigns,
  useCampaignsStore
} from 'src/store/campaign';

export default function useCampaigns({
  loadOnMount
}: { loadOnMount?: boolean } = {}) {
  const { data = [], run, error } = useAsync(fetchCampaigns);

  const isLoaded = useCampaignsStore(selectorCampaignsLoaded);
  const dataById = useCampaignsStore(selectorCampaignById);

  const loadCampaigns = () => {
    run().then((data) => {
      setCampaigns(data);
    });
  };

  useEffect(() => {
    if (isLoaded && !loadOnMount) {
      return;
    }

    loadCampaigns();
  }, []);

  return {
    isLoading: !isLoaded,
    data,
    dataById,
    error
  };
}
