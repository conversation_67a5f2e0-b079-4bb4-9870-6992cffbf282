import { fetchBoltOns } from 'src/api/boltons';
import { useAsync } from 'src/api/useApi';
import {
  setBoltOns,
  selectorBoltOnsLoaded,
  useBoltOnsStore,
  selectorSortedByStatusBoltOns,
  selectorBoltOnById,
  selectorBoltOnsDetails,
  selectAmountOfActiveBoltOns
} from 'src/store/boltons';

export default function useBoltonList() {
  const isBoltOnsLoaded = useBoltOnsStore(selectorBoltOnsLoaded);
  const boltOnsDetails = useBoltOnsStore(selectorBoltOnsDetails);
  const sortedBoltOnsByStatus = useBoltOnsStore(selectorSortedByStatusBoltOns);
  const activeBoltOns = sortedBoltOnsByStatus.filter(
    (bolton) => bolton.status.toLowerCase() === 'enabled'
  );
  const amountOfActiveBoltOns = useBoltOnsStore(selectAmountOfActiveBoltOns);

  const getBoltonById = useBoltOnsStore(selectorBoltOnById);

  const { run: doFetchBoltOns, isLoading } = useAsync(fetchBoltOns, {
    setToStore: setBoltOns,
    fetchOnMount: !isBoltOnsLoaded
  });

  return {
    isLoading: isLoading && !isBoltOnsLoaded,
    loadBoltOns: doFetchBoltOns,
    sortedBoltOnsByStatus,
    activeBoltOns,
    selectorBoltOnById,
    getBoltonById,
    boltOnsDetails,
    amountOfActiveBoltOns
  };
}
