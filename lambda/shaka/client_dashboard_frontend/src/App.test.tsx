import { describe, it, vi } from 'vitest';
import { render } from '@testing-library/react';
import App from './App';

describe('App', () => {
  it('renders learn react link', () => {
    const oldWindowLocation = window.location;
    // @ts-expect-error window
    delete window.location;
    // @ts-expect-error window
    window.location = Object.defineProperties(
      {},
      {
        ...Object.getOwnPropertyDescriptors(oldWindowLocation),
        replace: {
          configurable: true,
          value: vi.fn()
        }
      }
    );

    render(<App />);

    window.location = oldWindowLocation;
  });
});
