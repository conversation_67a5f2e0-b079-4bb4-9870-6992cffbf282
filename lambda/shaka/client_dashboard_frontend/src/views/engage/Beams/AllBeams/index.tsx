import LinkButton, { LinkColor } from 'src/components/Link';
import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import {
  getBeamDetailsRoute,
  getBeamEditRoute,
  ROUTES
} from 'src/config/navigation';
import AddIcon from 'src/icons/Add';
import Beam from './Beam';
import Tooltip from 'src/components/Tooltip';
import { getMessageScheduleCountdown } from 'src/helper';
import useBeams from 'src/hooks/useBeams';
import Loader from 'src/components/Loader';
import { getFormattedDate } from '../heplers';

export default function AllBeams() {
  const { isLoading, publishedBeams, scheduledBeams } = useBeams({
    loadOnMount: true
  });
  const hasScheduledBeams = scheduledBeams.length > 0;
  const hasPublishedBeams = publishedBeams.length > 0;

  const pageSubTitle = scheduledBeams.length
    ? 'Scheduled beams'
    : 'Published beams';

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div>
      <div className="w-full flex justify-between pb-8 gap-4 items-center">
        <Typography>{pageSubTitle}</Typography>
        <LinkButton
          to={ROUTES.BEAMS_ADD}
          color={LinkColor.GrayGradient}
          icon={<AddIcon />}
        >
          Add new beam
        </LinkButton>
      </div>
      {!hasScheduledBeams && !hasPublishedBeams && (
        <Typography shade={TypographyShade.Light}>No beams found</Typography>
      )}
      {hasScheduledBeams && (
        <div className="space-y-4">
          {scheduledBeams.map(({ message, send_on, id, title }) => (
            <Beam
              key={id}
              to={getBeamEditRoute(id)}
              message={message}
              title={title}
              publishingTime={
                <Typography>
                  Live in{' '}
                  <Tooltip
                    text={getFormattedDate(send_on!)}
                    textSize={TypographySize.BodyM}
                    wrapperClassName="inline-block"
                    colorScheme="flat"
                    near
                  >
                    <Typography color={TypographyColor.Success}>
                      {getMessageScheduleCountdown(send_on!)}
                    </Typography>
                  </Tooltip>
                </Typography>
              }
            />
          ))}
        </div>
      )}
      {hasPublishedBeams && (
        <div className="mt-10">
          {hasScheduledBeams && <Typography>Published beams</Typography>}
          <div className="mt-4 space-y-4">
            {publishedBeams.map(
              ({ message, id, status, title, delivery_report }) => (
                <Beam
                  key={id}
                  to={getBeamDetailsRoute(id)}
                  message={message}
                  title={title}
                  status={status}
                  deliveredAmount={delivery_report?.total_delivered}
                  publishingTime={
                    delivery_report?.send_date && (
                      <Typography>
                        Sent on {getFormattedDate(delivery_report?.send_date)}
                      </Typography>
                    )
                  }
                />
              )
            )}
          </div>
        </div>
      )}
    </div>
  );
}
