import Card from 'src/components/Card';
import Checkbox from 'src/components/form/Checkbox';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import Textarea from 'src/components/form/Textarea';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import { beamPublishingOptions } from './constants';
import Calendar from 'src/components/form/Calendar';
import { useForm } from 'react-hook-form';
import { Inputs } from './types';
import Link from 'src/components/Link';
import { ROUTES } from 'src/config/navigation';
import { ButtonColor } from 'src/components/sharedStyles';
import Button from 'src/components/Button';
import { schema } from './schema';
import { yupResolver } from '@hookform/resolvers/yup';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { BeamStatus } from 'src/types/sms';
import { addBeam, deleteBeam, updateBeam } from 'src/api/sms';
import { useEffect, useState } from 'react';
import useBeams from 'src/hooks/useBeams';
import Input from 'src/components/form/Input';
import { DateTime } from 'luxon';
import { useAsync } from 'src/api/useApi';
import ErrorText from 'src/components/ErrorText';
import Typography, { TypographySize } from 'src/components/Typography';
import { twMerge } from 'tailwind-merge';
import WarningIcon from 'src/icons/Warning';

const minBeamDate = DateTime.now()
  .toFormat('yyyy-MM-dd hh:mm')
  .replace(' ', 'T');

const infoText =
  'This is used to identify beams for Dashboard users, it is not visible to subscribers.';

export default function BeamView() {
  const { beamId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { isLoading, beamsById, loadBeams } = useBeams({
    loadOnMount: true
  });
  const { run: doAddBeam, error: creationError } = useAsync(addBeam);
  const { run: doUpdateBeam, error: updatingError } = useAsync(updateBeam);
  const { run: doDeleteBeam, error: deletingError } = useAsync(deleteBeam);

  // TODO: remove after useAsync refactor
  const [isLoadingRequest, setIsLoadingRequest] = useState(false);

  const error = creationError || updatingError || deletingError;

  const duplicatedBeamId = new URLSearchParams(location.search).get('beamId');
  const isEdit = Boolean(beamId);
  const submitButtonLabel = isEdit ? 'Save changes' : 'Send beam';
  const title = isEdit ? 'Edit beam' : 'Create a new beam';

  const {
    register,
    watch,
    setValue,
    handleSubmit,
    reset,
    control,
    formState: { errors }
  } = useForm<Inputs>({
    defaultValues: {
      status: BeamStatus.SENDING
    },
    resolver: yupResolver(schema)
  });

  const beamStatus = watch('status');
  const watchSendOn = watch('send_on');

  const isWithinBusinessHours = () => {
    const date = watchSendOn ? DateTime.fromISO(watchSendOn) : DateTime.now();
    return (
      date.weekday >= 1 &&
      date.weekday <= 5 &&
      date.hour >= 8 &&
      date.hour <= 17
    );
  };

  const redirectToBeams = () => {
    loadBeams();
    navigate(ROUTES.BEAMS);
  };

  const onSubmit = (data: Inputs) => {
    setIsLoadingRequest(true);
    const formData = {
      title: data.title,
      message: data.message,
      ...(data.send_on && { send_on: data.send_on })
    };

    if (isEdit) {
      doUpdateBeam({
        id: Number(beamId),
        ...formData
      }).then(() => {
        redirectToBeams();
      });

      return;
    }

    doAddBeam(formData).then(() => {
      redirectToBeams();
    });
  };

  const onDelete = () => {
    setIsLoadingRequest(true);
    doDeleteBeam(Number(beamId)).then(() => {
      redirectToBeams();
    });
  };

  useEffect(() => {
    if (beamId || duplicatedBeamId) {
      const selectedBeamId = beamId || duplicatedBeamId;
      const selectedBeam = beamsById[Number(selectedBeamId)];

      if (!selectedBeam) return;

      const formData = {
        title: selectedBeam.title,
        message: selectedBeam.message,
        status: beamId ? selectedBeam.status : BeamStatus.SENDING,
        send_on: selectedBeam.send_on?.slice(0, 16)
      };

      reset(formData);
    }
  }, [beamId, isLoading, duplicatedBeamId]);

  useEffect(() => {
    if (error && isLoadingRequest) {
      setIsLoadingRequest(false);
    }
  }, [error, isLoadingRequest]);

  return (
    <Card title={title} isLoading={isLoading}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-3 gap-16">
          <div className="col-span-3 xl:col-span-2 space-y-8">
            <LabeledInputWrapper label="Title" info={infoText}>
              <Input {...register('title')} />
            </LabeledInputWrapper>

            <LabeledInputWrapper label="Choose beaming channels">
              <div className="flex items-center gap-6">
                <div className="inline-block rounded-xl bg-white/[0.05] px-5 py-3 min-w-[190px]">
                  <Checkbox
                    checked
                    label="SMS"
                    name="SMS"
                    onChange={() => {}}
                  />
                </div>
                <Checkbox
                  label="WhatsApp (coming soon)"
                  name="whatsapp"
                  onChange={() => {}}
                  disabled
                />
              </div>
            </LabeledInputWrapper>

            <LabeledInputWrapper
              label="Beam content"
              error={errors.message?.message}
            >
              <Textarea
                placeholder="Type your message here"
                name="message"
                control={control}
                countChars
                height={360}
              />
            </LabeledInputWrapper>

            <div className="relative">
              <LabeledInputWrapper
                label="Publish time"
                error={errors.send_on?.message}
              >
                <div className="flex max-xl:flex-wrap items-center gap-6">
                  <RadioButtonGroup
                    horizontal
                    value={beamStatus}
                    options={beamPublishingOptions}
                    onChange={(value) => {
                      setValue('status', value as BeamStatus);
                    }}
                  />
                  {beamStatus === BeamStatus.TO_SEND && (
                    <div className="inline-block grow">
                      <Calendar {...register('send_on')} min={minBeamDate} />
                    </div>
                  )}
                </div>
              </LabeledInputWrapper>

              {!isWithinBusinessHours() && (
                <div
                  className={twMerge(
                    'flex justify-between gap-4',
                    'w-1/2 p-5 bg-gradient-tooltip-3 -right-10 bottom-0 text-lg',
                    'xl:w-1/2 xl:absolute xl:translate-x-full xl:rounded-xl xl:px-8 xl:py-10 2xl:text-[22px]'
                  )}
                >
                  <Typography size={TypographySize.Inherit}>
                    Warning, this Beam is being sent outside of working hours,
                    please be mindful when your subscribers will be receiving
                    this communication.
                  </Typography>
                  <div className="min-w-9">
                    <WarningIcon className="w-9 h-9" />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <ErrorText error={error} />

        <div className="flex justify-between pt-10">
          <div />

          <div className="flex justify-center gap-5">
            <Link to={ROUTES.BEAMS}>Cancel</Link>

            <Button
              type="submit"
              color={ButtonColor.Pink}
              isLoading={isLoadingRequest}
            >
              {submitButtonLabel}
            </Button>
          </div>
          {isEdit ? (
            <Button onClick={onDelete} isLoading={isLoadingRequest}>
              Delete beam
            </Button>
          ) : (
            <div />
          )}
        </div>
      </form>
    </Card>
  );
}
