import { Controller, useFormContext } from 'react-hook-form';
import { eligibilityTypeOptions, yesNoOptions } from '../constants';
import InputWrapper from 'src/components/form/WrapperWithLabel';
import Dropdown from 'src/components/form/Dropdown';
import Input from 'src/components/form/Input';
import { Inputs } from '../types';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import NumberInput from 'src/components/form/NumberInput';

const targetOptions = [
  { value: 'everyone', label: 'Everyone' }
  // { value: 'specific', label: 'Specific group' }
];

export default function Eligibility() {
  const {
    control,
    register,
    watch,
    formState: { errors }
  } = useFormContext<Inputs>();

  const watchEligibilityType = watch('eligibility_type');
  const watchEligibilityTarget = watch('eligibility_target');

  return (
    <div className="w-[314px] my-20 space-y-14">
      <InputWrapper label="Free claim eligibility">
        <Dropdown
          control={control}
          name="eligibility_type"
          options={eligibilityTypeOptions}
          shape="square"
        />
      </InputWrapper>

      {watchEligibilityType === 'tenure' && (
        <InputWrapper
          label="Tenure amount"
          postInputLabel="month(s)"
          error={errors.eligibility?.amount?.message}
        >
          <NumberInput
            {...register('eligibility.amount')}
            placeholder="Enter months"
            integerOnly
          />
        </InputWrapper>
      )}

      {watchEligibilityType === 'total_points_earned' && (
        <InputWrapper
          label="Points amount"
          error={errors.eligibility?.amount?.message}
        >
          <div className="flex gap-4 items-center">
            <Input
              {...register('eligibility.amount')}
              placeholder="Enter the points amount"
            />
          </div>
        </InputWrapper>
      )}

      {watchEligibilityType === 'total_spend' && (
        <InputWrapper
          label="Spend amount"
          error={errors.eligibility?.amount?.message}
        >
          <div className="flex gap-4 items-center">
            <Input
              {...register('eligibility.amount')}
              placeholder="Enter spend amount (£)"
            />
          </div>
        </InputWrapper>
      )}

      {watchEligibilityType === 'airdrop' && (
        <InputWrapper label="Target">
          <div className="flex gap-4 items-center">
            <Controller
              name="eligibility_target"
              control={control}
              render={({ field: { value = 'everyone', onChange } }) => (
                <div className="flex gap-10">
                  <RadioButtonGroup
                    options={targetOptions}
                    horizontal
                    value={value}
                    onChange={onChange}
                  />
                  {watchEligibilityTarget === 'specific' && (
                    <Dropdown
                      control={control}
                      name="eligibility.target_group"
                      options={yesNoOptions}
                      shape="square"
                    />
                  )}
                </div>
              )}
            />
          </div>
        </InputWrapper>
      )}
    </div>
  );
}
