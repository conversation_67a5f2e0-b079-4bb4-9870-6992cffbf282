import * as yup from 'yup';
import { Inputs, PerkStatus } from './types';

const testNumber = (schema: yup.StringSchema, fieldName: string) =>
  schema
    .test('isNumber', `${fieldName} must be a number`, (value) => {
      return !isNaN(Number(value));
    })
    .test('isPositive', `${fieldName} must be positive`, (value) => {
      return Number(value) > 0;
    });

const stringAsNumberFieldValidation = (fieldName: string) =>
  testNumber(yup.string().required(`${fieldName} is required`), fieldName);

export const schema = yup
  .object({
    name: yup.string().required('Name is required'),
    image: yup.string().required('Image is required'),
    pointsCost: yup.string().when('use_points', ([use_points], schema) => {
      return use_points === 'yes'
        ? schema.required('Points amount is required')
        : schema.optional();
    }),
    availability_date: yup
      .string()
      .when(['availability'], ([availability], schema) => {
        return availability === PerkStatus.Inactive
          ? schema.required('Date is required')
          : schema.optional();
      }),
    use_points: yup
      .string()
      .when('eligibility_type', ([eligibility_type], schema) => {
        if (eligibility_type !== 'no_free') {
          return schema.optional();
        }

        return schema
          .required(
            'Perk needs a free eligibility option or to be redeemed for points'
          )
          .test(
            'isYes',
            'For "No free claim eligibility" points amount is required',
            (value) => value === 'yes'
          );
      }),
    eligibility: yup
      .object()
      .when('eligibility_type', ([eligibility_type], schema) => {
        if (eligibility_type === 'airdrop' || eligibility_type === 'no_free') {
          return schema.optional();
        }

        return schema.shape({
          amount: stringAsNumberFieldValidation('Amount')
        });
      }),
    redemption_limit: yup
      .string()
      .when(['redemption'], ([redemption], schema) => {
        return redemption === 'limited'
          ? schema.required('Redemption value is required')
          : schema.optional();
      }),
    discount_details: yup.object().when('type', ([type], schema) => {
      return type === 'discount'
        ? schema.shape({
            discount_amount: yup
              .string()
              .when(['discount_type'], ([discount_type], schema) => {
                return discount_type === 'flat'
                  ? testNumber(schema, 'Discount amount')
                  : schema.optional().nullable();
              }),
            discount_percentage: yup
              .number()
              .when(['discount_type'], ([discount_type], schema) => {
                return discount_type === 'percentage'
                  ? schema
                      .min(0, 'Discount percentage must be bigger than 0')
                      .max(
                        100,
                        'Discount percentage must be less than or equal to 100'
                      )
                  : schema.optional().nullable();
              }),
            discount_duration_months:
              stringAsNumberFieldValidation('Discount duration')
          })
        : schema.optional();
    })
  })
  .required() as yup.ObjectSchema<Inputs>;
