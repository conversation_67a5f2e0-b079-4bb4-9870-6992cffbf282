import { useFormContext } from 'react-hook-form';
import InputWrapper from 'src/components/form/WrapperWithLabel';
import { Inputs } from '../types';
import Dropdown from 'src/components/form/Dropdown';
import Input from 'src/components/form/Input';
import NumberInput from 'src/components/form/NumberInput';

const discountTypeOptions = [
  { value: 'percentage', label: 'Percentage' },
  { value: 'flat', label: 'Amount' }
];

export default function Discount() {
  const {
    control,
    register,
    watch,
    formState: { errors }
  } = useFormContext<Inputs & { type: 'discount' }>();

  const watchDiscountType = watch('discount_details.discount_type');

  const isFlat = watchDiscountType === 'flat';

  return (
    <div className="w-1/3 space-y-10">
      <InputWrapper label="Discount type">
        <Dropdown
          control={control}
          name="discount_details.discount_type"
          options={discountTypeOptions}
          shape="square"
        />
      </InputWrapper>

      <InputWrapper
        label="Discount amount"
        info="Subscribers can only allocate a discount to a single plan."
        postInputLabel={isFlat ? '£' : '%'}
        error={
          errors.discount_details?.discount_amount?.message ||
          errors.discount_details?.discount_percentage?.message
        }
      >
        <div className="w-1/3">
          {isFlat ? (
            <Input {...register('discount_details.discount_amount')} />
          ) : (
            <NumberInput
              integerOnly
              {...register('discount_details.discount_percentage', {
                valueAsNumber: true
              })}
            />
          )}
        </div>
      </InputWrapper>
      <InputWrapper
        label="Discount duration"
        postInputLabel="months"
        error={errors.discount_details?.discount_duration_months?.message}
      >
        <div className="w-1/3">
          <NumberInput
            {...register('discount_details.discount_duration_months')}
          />
        </div>
      </InputWrapper>
    </div>
  );
}
