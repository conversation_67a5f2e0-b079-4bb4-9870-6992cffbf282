import { Controller, useFormContext } from 'react-hook-form';
import InputWrapper from 'src/components/form/WrapperWithLabel';
import { Inputs } from '../types';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import NumberInput from 'src/components/form/NumberInput';

export const limitOptions = [
  { value: 'unlimited', label: 'Unlimited' },
  { value: 'limited', label: 'Limited' }
];

export default function RedemptionLimit() {
  const {
    control,
    register,
    watch,
    formState: { errors }
  } = useFormContext<Inputs>();

  const watchRedemption = watch('redemption');

  return (
    <div className="my-20 space-y-14">
      <InputWrapper
        label="Redemption limit"
        error={errors.redemption_limit?.message}
      >
        <Controller
          name="redemption"
          control={control}
          render={({ field: { value, onChange } }) => (
            <div className="flex items-center gap-8 h-12">
              <RadioButtonGroup
                horizontal
                value={value}
                options={limitOptions}
                onChange={onChange}
              />
              {watchRedemption === 'limited' && (
                <NumberInput
                  {...register('redemption_limit')}
                  placeholder="Enter limit amount"
                  positiveOnly
                  integerOnly
                />
              )}
            </div>
          )}
        />
      </InputWrapper>
    </div>
  );
}
