import { useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import CsvDownloader from 'react-csv-downloader';
import Card from 'src/components/Card';
import InfoBlock from 'src/components/InfoBlock';
import InfoCard from 'src/components/InfoCard';
import Status, { StatusColor } from 'src/components/Status';
import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import usePerks from 'src/hooks/usePerks';
import ImageIcon from 'src/icons/Image';
import { selectorPerkById, usePerksStore } from 'src/store/perks';
import { Perk } from 'src/types/perks';
import { eligibilityTypeOptions } from '../PerkView/constants';
import { getPerksEditRoute, ROUTES } from 'src/config/navigation';
import Link from 'src/components/Link';
import Button, { ButtonColor } from 'src/components/Button';
import { currencyRounded, numberInStringRounded } from 'src/helper';
import HistoryTable, { COLUMNS as HistoryColumns } from './HistoryTable';
import SubscribersTable, {
  COLUMNS as SubscriberColumns
} from './SubscribersTable';
import { Tab } from '@headlessui/react';
import { DateTime } from 'luxon';
import AddIcon from 'src/icons/Add';
import { deletePerk, getPerkHistory, getPerkSubscribers } from 'src/api/perks';
import { useAsync } from 'src/api/useApi';
import NotificationDialog from 'src/components/NotificationDialog';
import useClientData from 'src/hooks/useClient';

const tabs = ['Redemption history', 'Subscriber redemption'];

const getEligibilityAmountLabel = (
  amount: Perk['eligibility_threshold'],
  type: Perk['eligibility_type']
) => {
  if (type === 'tenure') {
    return `${numberInStringRounded(amount)} months`;
  }

  return amount;
};

export default function PerkDetails() {
  const { perkId = '' } = useParams();
  const { isLoading, loadPerks } = usePerks();
  const perkById = usePerksStore(selectorPerkById(perkId));
  const { clientData } = useClientData();
  const navigate = useNavigate();

  const { data: historyData = [], isLoading: loadingHistory } = useAsync(
    getPerkHistory,
    {
      fetchOnMount: Boolean(perkId),
      props: [perkId]
    }
  );
  const { data: subscriberData = [], isLoading: loadingSubscribers } = useAsync(
    getPerkSubscribers,
    {
      fetchOnMount: Boolean(perkId),
      props: [perkId]
    }
  );

  const [selectedTab, setSelectedTab] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const fileName = selectedTab
    ? `${
        clientData.name
      }-subscriber_redemption-${DateTime.now().toLocaleString()}.csv`
    : `${
        clientData.name
      }-redemption_history-${DateTime.now().toLocaleString()}.csv`;
  const csvColumns: Record<string, string> =
    selectedTab === 0 ? HistoryColumns : SubscriberColumns;

  const csvData = useMemo(
    () =>
      selectedTab === 0
        ? historyData
        : subscriberData?.map(({ user, ...restProps }) => ({
            ...restProps,
            fullName: user.full_name
          })),
    [selectedTab, historyData, subscriberData]
  );

  const isExportButtonVisible =
    (selectedTab === 0 && historyData && historyData.length > 0) ||
    (selectedTab === 1 && subscriberData && subscriberData.length > 0);

  const {
    name,
    enabled,
    perk_image,
    description,
    eligibility_type,
    eligibility_threshold,
    elective_redemption_cost,
    redemptions_amount,
    redemption_limit,
    total_cost,
    remaining_quantity,
    discount_details,
    voucher_details,
    bolt_on_details
  } = perkById || ({} as Perk);

  const perkType = useMemo(() => {
    if (discount_details) {
      return 'Discount';
    }
    if (voucher_details) {
      return 'Virtual / Voucher';
    }
    if (bolt_on_details) {
      return 'Bolt on';
    }

    return '';
  }, [perkById]);

  const firstLineData = useMemo(
    () => [
      {
        title: 'redemptions',
        value: redemptions_amount
      },
      {
        title: 'quantity remaining',
        value: redemption_limit ? remaining_quantity : '∞'
      },
      { title: 'total cost', value: currencyRounded(Number(total_cost)) }
    ],
    [perkById]
  );

  const secondLineData = useMemo(
    () => [
      {
        title: 'claim eligibility',
        value:
          eligibilityTypeOptions.find(
            (eligibility) => eligibility.value === eligibility_type
          )?.label || 'Unknown'
      },
      {
        title: 'eligibility amount',
        value: getEligibilityAmountLabel(
          eligibility_threshold,
          eligibility_type
        )
      },
      {
        title: 'points to redeem',
        value: numberInStringRounded(elective_redemption_cost) || 0
      }
    ],
    [perkById]
  );

  const handlePerkDelete = () => {
    if (perkId) {
      deletePerk(perkId).then(() => {
        loadPerks();
        navigate(ROUTES.PERKS);
      });
    }
  };

  const showDeleteConfirmation = () => {
    setIsDeleteDialogOpen(true);
  };

  const hideDeleteConfirmation = () => {
    setIsDeleteDialogOpen(false);
  };

  return (
    <Card
      title="Perks"
      subtitle={name || 'Perk Details'}
      isLoading={isLoading || loadingHistory || loadingSubscribers}
      actions={
        perkId &&
        (enabled ? (
          <Status status="Active" color={StatusColor.Success} />
        ) : (
          <Status status="Inactive" color={StatusColor.Error} />
        ))
      }
    >
      <div className="mb-6">
        <Typography>{description}</Typography>
      </div>

      <InfoCard
        classes="grid grid-cols-4 gap-5 h-[280px] mb-7"
        gradientSize="big"
      >
        <div className="h-full flex justify-center items-center text-8xl rounded-2xl uppercase text-black/60 overflow-hidden">
          {perk_image ? (
            <img src={perk_image} className="h-full w-full object-cover" />
          ) : (
            <span className="h-full w-full flex items-center justify-center bg-white/5 ">
              <ImageIcon />
            </span>
          )}
        </div>
        {firstLineData.map((data) => (
          <InfoBlock
            {...data}
            key={data.title}
            titleColor={TypographyColor.Success}
            bold
          />
        ))}
      </InfoCard>

      <InfoCard classes="grid grid-cols-4 gap-5 h-[280px]" gradientSize="big">
        <div className="h-full flex justify-center items-center">
          <Typography size={TypographySize.Title}>{perkType}</Typography>
        </div>
        {secondLineData.map((data) => (
          <InfoBlock
            {...data}
            key={data.title}
            bold
            fontSize={TypographySize.Title}
          />
        ))}
      </InfoCard>

      <div className="flex justify-end gap-4 mt-9">
        <Link to={getPerksEditRoute(perkId!)}>Edit perk</Link>
        <Button onClick={showDeleteConfirmation}>Delete perk</Button>
      </div>

      <div className="block w-full h-[1px] bg-white/[0.14] my-12" />

      <Tab.Group onChange={setSelectedTab}>
        <div className="flex justify-between items-center mb-10">
          <Tab.List className="flex gap-8">
            {tabs.map((tab) => (
              <Tab key={tab} className={'focus:outline-none'}>
                {({ selected }) => (
                  <Typography
                    size={TypographySize.Title}
                    shade={selected ? undefined : TypographyShade.Light}
                  >
                    {tab}
                  </Typography>
                )}
              </Tab>
            ))}
          </Tab.List>
          {isExportButtonVisible && (
            <CsvDownloader
              filename={fileName}
              columns={Object.keys(csvColumns).map((key) => ({
                id: key,
                displayName: csvColumns[key] as string
              }))}
              datas={csvData as any}
            >
              <Button
                color={ButtonColor.GrayGradient}
                className="flex gap-2 items-center"
                leftIcon={<AddIcon />}
              >
                <span>Export to CSV</span>
              </Button>
            </CsvDownloader>
          )}
        </div>
        <Tab.Panels>
          <Tab.Panel>
            {historyData?.length ? (
              <HistoryTable data={historyData} />
            ) : (
              <div className="flex justify-center">
                <Typography>No redemption history</Typography>
              </div>
            )}
          </Tab.Panel>
          <Tab.Panel>
            {subscriberData?.length ? (
              <SubscribersTable data={subscriberData} />
            ) : (
              <div className="flex justify-center">
                <Typography>No subscriber redemption</Typography>
              </div>
            )}
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>

      <NotificationDialog
        isOpen={isDeleteDialogOpen}
        onCancel={hideDeleteConfirmation}
        title="Delete perk?"
        description="If you delete this perk, you will lose all the data and subscribers will no longer be able to redeem it."
        submitButtonText="Delete"
        cancelActionColor="default"
        onSubmit={handlePerkDelete}
        cancelButtonText="Cancel"
      />
    </Card>
  );
}
