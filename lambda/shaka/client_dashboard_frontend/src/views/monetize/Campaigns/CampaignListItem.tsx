import { DateTime } from 'luxon';
import { useMemo } from 'react';
import Typography, {
  TypographyColor,
  TypographySize
} from 'src/components/Typography';
import { Campaign, Eligibility } from 'src/types/campaign';
import { campaignTargetOptions } from './contants';
import { Link } from 'react-router-dom';
import { getCampaignEditRoute } from 'src/config/navigation';

export const getFormattedDate = (date: string) => {
  const dateTime = DateTime.fromISO(date);
  return dateTime.toLocaleString() + " @ " + dateTime.toFormat('HH:mm');
};

const getTiming = (campaign: Campaign) => {
  const notStarted = DateTime.fromISO(campaign.start_date) > DateTime.now();
  if (notStarted) {
    return (
      <Typography size={TypographySize.BodyS}>
        Starts on{' '}
        <Typography color={TypographyColor.Success}>
          {getFormattedDate(campaign.start_date)}
        </Typography>
      </Typography>
    );
  }
  const ended = DateTime.fromISO(campaign.end_date) < DateTime.now();
  if (ended) {
    return (
      <Typography size={TypographySize.BodyS}>
        Expired on{' '}
        <Typography color={TypographyColor.Error}>
          {getFormattedDate(campaign.end_date)}
        </Typography>
      </Typography>
    );
  }

  const timeDiff = DateTime.fromISO(campaign.end_date)
    .diffNow('days')
    .days.toFixed();

  return (
    <Typography size={TypographySize.BodyS}>
      Valid for{' '}
      <Typography color={TypographyColor.Success}>
        {timeDiff} more {parseInt(timeDiff) === 1 ? 'day' : 'days'}
      </Typography>
    </Typography>
  );
};

const getDuration = (campaign: Campaign) => {
  const duration = campaign.marketing_eligibility.active_duration_months;
  return duration
    ? `${duration} month${duration > 1 ? 's' : ''} live`
    : 'Indefinite';
};

export default function CampaignListItem({ campaign }: { campaign: Campaign }) {
  const isDurationVisible =
    campaign.marketing_eligibility.eligibility_type ===
    Eligibility.SUBSCRIBER_ACTIVITY;

  const eligibility = useMemo(() => {
    return campaignTargetOptions.find(
      (option) =>
        option.value === campaign.marketing_eligibility.eligibility_type
    );
  }, [campaign.marketing_eligibility.eligibility_type]);

  return (
    <Link to={getCampaignEditRoute(campaign.id)} className="block">
      <div className="bg-cover cursor-pointer bg-[#13161F]/80 hover:bg-[url(/monetize/campaign-bg.png)] py-4 px-6 rounded-3xl flex justify-between ">
        <div className="grow flex flex-col items-start gap-6">
          <div className="flex gap-4">
            <div className="rounded-xl bg-[#D9D9D9]/[.03] py-2 px-6">
              <Typography>{eligibility?.label}</Typography>
            </div>
            {isDurationVisible && (
              <div className="rounded-xl bg-[#D9D9D9]/[.03] py-2 px-6">
                <Typography>{getDuration(campaign)}</Typography>
              </div>
            )}
          </div>
          <div className="flex flex-col items-start gap-2">
            <Typography size={TypographySize.Title} bold>
              {campaign.title}
            </Typography>
            <Typography size={TypographySize.BodyS}>
              {getTiming(campaign)}
            </Typography>
          </div>
          <Typography size={TypographySize.Caption}>
            {campaign.discount_type_summary}
          </Typography>
        </div>
        <div className="rounded-2xl bg-[#D9D9D9]/[.03] my-5 text-[32px] px-6 flex items-center justify-center w-[206px]">
          <Typography size={TypographySize.Inherit} bold>
            {campaign.discount_value_summary}
          </Typography>
        </div>
      </div>
    </Link>
  );
}
