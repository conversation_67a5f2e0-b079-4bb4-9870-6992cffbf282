import { CampaignType, DiscountType, Eligibility } from 'src/types/campaign';

export type PlanDiscounts = {
  plan?: number;
  discount_type: DiscountType;
  discount_percentage?: number | string | null;
  discount_amount?: number | string | null;
  discount_duration_months: number;
};

export type Inputs = {
  title: string;
  eligibility: Eligibility;
  activeDurationMonths: number;
  campaignType: CampaignType;
  startDate: string;
  endDate: string;
  planDiscounts: Record<number, PlanDiscounts>;
  selectedPlans: number[];
};
