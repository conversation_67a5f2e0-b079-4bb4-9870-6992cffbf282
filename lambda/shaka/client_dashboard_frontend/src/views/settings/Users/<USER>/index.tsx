import Card from 'src/components/Card';
import Input from 'src/components/form/Input';
import Button, { ButtonColor } from 'src/components/Button';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from 'src/config/navigation';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import Link from 'src/components/Link';
import { Inputs } from './types';
import Dropdown from 'src/components/form/Dropdown';
import { UserRole } from 'src/types/users';
import {
  selectorSetUsers,
  selectorUserById,
  selectorUsersLoaded,
  useUsersStore
} from 'src/store/users';
import { useEffect } from 'react';
import { useAsync } from 'src/api/useApi';
import { createUser, fetchUsers, updateUser } from 'src/api/users';
import { roleOptions } from '../constants';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import Typography, { TypographyColor } from 'src/components/Typography';
import { capitalize } from 'src/helper';

const schema = yup.object({
  email: yup.string().required('Email is required').email(),
  role: yup.mixed<UserRole>().defined()
});

export default function UserView() {
  const { userId } = useParams();
  const isCreating = !userId;
  const navigation = useNavigate();

  const { run: doGetUserList } = useAsync(fetchUsers);
  const { run: doCreateUser, error: createError } = useAsync(createUser);
  const { run: doUpdateUser, error: updateError } = useAsync(updateUser);

  const error = createError || updateError;

  const user = useUsersStore(selectorUserById(userId));
  const setUsers = useUsersStore(selectorSetUsers);
  const isUsersLoaded = useUsersStore(selectorUsersLoaded);

  const methods = useForm<Inputs>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: '',
      role: UserRole.Member
    }
  });

  const { register, handleSubmit, control, reset } = methods;

  const submitButtonLabel = isCreating ? 'Create user' : 'Update user';

  const handleGetUsers = () => doGetUserList().then((users) => setUsers(users));

  useEffect(() => {
    if (userId && !user) {
      handleGetUsers();
    }
  }, []);

  useEffect(() => {
    if (user) {
      reset(user);
    }
  }, [user]);

  const returnToUsers = () => {
    handleGetUsers();
    navigation(ROUTES.USERS);
  };

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    if (isCreating || !user) {
      doCreateUser(data).then(returnToUsers);
    } else {
      doUpdateUser(userId, data).then(returnToUsers);
    }
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card
          title="New user creation"
          bottomActions={
            <>
              <Link to={ROUTES.USERS}>Cancel</Link>
              <Button
                type="submit"
                color={ButtonColor.Pink}
                className="hover:opacity-70"
              >
                {submitButtonLabel}
              </Button>
            </>
          }
          isLoading={Boolean(userId) && !isUsersLoaded}
          fullHeight
        >
          <LabeledInputWrapper label="Email">
            <Input placeholder="<EMAIL>" {...register('email')} />
          </LabeledInputWrapper>
          <LabeledInputWrapper label="Role">
            <Dropdown control={control} name="role" options={roleOptions} />
          </LabeledInputWrapper>

          {error &&
            Object.values<string[]>(error).map((error) => (
              <Typography color={TypographyColor.Error} key={error[0]}>
                {capitalize(error[0])}
              </Typography>
            ))}
        </Card>
      </form>
    </FormProvider>
  );
}
