@keyframes rotate {
    0% {
        transform: rotate3d(0, 0, 0, 0deg);
    }

    30% {
        transform: rotate3d(0, 1, -1, 90deg);
    }

    100% {
        transform: rotate3d(1, 1, 0, -0.5turn);
    }
}

@keyframes rotate-back {
    0% {
        transform: rotate3d(1, 1, 0, -0.5turn);
    }

    50% {
        transform: rotate3d(0, 1, -1, 90deg);
    }

    100% {
        transform: rotate3d(0, 0, 0, 0deg);
    }
}

.card {
    perspective: 1000px;
}

.card-content {
    transform-style: preserve-3d;
    transform-origin: center;
}

.card.rotated .card-content {
    animation: rotate 0.35s linear forwards;
}

.card.rotated-back .card-content {
    animation: rotate-back 0.35s linear;
}


.side-front,
.side-back {
    backface-visibility: hidden;
}

.side-back {
    transform: rotateY(.5turn);
}