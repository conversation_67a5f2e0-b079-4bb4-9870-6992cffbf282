import { Subscriber } from 'src/types/subscribers';

type SubscriberColumn =
  | keyof Omit<
      Subscriber,
      'id' | 'email' | 'latest_number' | 'latest_number_display'
    >
  | 'action';

export const SUBSCRIBER_COLUMNS: Record<SubscriberColumn, string> = {
  name: 'Name',
  group: 'Group',
  revenue_generated: 'Revenue',
  status: 'Status',
  start_date: 'Duration',
  latest_plan_name: 'Latest plan',
  action: 'Action'
};

export type CsvKey = keyof Omit<
  Subscriber,
  'id' | 'latest_number' | 'latest_number_display'
>;

export const SUBSCRIBER_COLUMNS_CSV: Record<CsvKey, string> = {
  name: 'Name',
  group: 'Group',
  email: 'Email',
  revenue_generated: 'Revenue generated',
  status: 'Status',
  start_date: 'Member since',
  latest_plan_name: 'Latest plan'
};
