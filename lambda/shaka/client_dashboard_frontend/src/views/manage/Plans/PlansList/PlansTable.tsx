import { createColumnHelper } from '@tanstack/react-table';
import Table from 'src/components/Table';
import { toLocalTime, formatNumber } from 'src/helper';
import { Plan, PLAN_COLUMNS } from '../constants';

type Props = {
  data: Plan[];
};

const headerClass = 'text-left font-extrabold py-4 pl-6 pr-3';
const cellClass = 'py-3 pl-6 pr-3';
const columnHelper =
  createColumnHelper<
    Pick<
      Plan,
      | 'name'
      | 'active_subscribers'
      | 'revenue_per_subscriber'
      | 'total_plan_revenue'
      | 'created_date'
    >
  >();
const columns = [
  columnHelper.accessor('name', {
    cell: (info) => <div className={cellClass}>{info.getValue()}</div>,
    header: () => <div className={headerClass}>{PLAN_COLUMNS.name}</div>
  }),
  columnHelper.accessor('active_subscribers', {
    cell: (info) => <div className={cellClass}>{info.getValue()}</div>,
    header: () => (
      <div className={headerClass}>{PLAN_COLUMNS.active_subscribers}</div>
    )
  }),
  columnHelper.accessor('revenue_per_subscriber', {
    cell: (info) => (
      <div className={cellClass}>
        {formatNumber(info.getValue(), { style: 'currency', currency: 'GBP' })}
      </div>
    ),
    header: () => (
      <div className={headerClass}>{PLAN_COLUMNS.revenue_per_subscriber}</div>
    )
  }),
  columnHelper.accessor('total_plan_revenue', {
    cell: (info) => (
      <div className={cellClass}>
        {formatNumber(info.getValue(), { style: 'currency', currency: 'GBP' })}
      </div>
    ),
    header: () => (
      <div className={headerClass}>{PLAN_COLUMNS.total_plan_revenue}</div>
    )
  }),
  columnHelper.accessor('created_date', {
    cell: (info) => (
      <div className={cellClass + ' text-right'}>
        {toLocalTime(info.getValue())}
      </div>
    ),
    header: () => (
      <div className="text-right font-extrabold py-4 pl-6 pr-3">
        {PLAN_COLUMNS.created_date}
      </div>
    )
  })
];

export default function PlansTable({ data }: Props) {
  return (
    <Table
      columns={columns}
      data={data}
    />
  );
}
