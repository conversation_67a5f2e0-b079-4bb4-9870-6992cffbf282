import Card from 'src/components/Card';
import { ROUTES } from 'src/config/navigation';
import usePlansOffering from 'src/hooks/usePlansOffering';
import { selectorPlans, usePlansStore } from 'src/store/plan';
import usePlanList from 'src/hooks/usePlanList';
import Link, { LinkColor } from 'src/components/Link';
import AddIcon from 'src/icons/Add';
import StatsCard from './StatsCard';

export default function PlansList() {
  const { isLoading: isOfferingLoading } = usePlansOffering();
  const { isLoading: isPlansLoading } = usePlanList();
  const plans = usePlansStore(selectorPlans);

  const isDataLoading = isPlansLoading || isOfferingLoading;

  return (
    <Card
      simple
      isLoading={isDataLoading}
      actions={
        <Link
          to={ROUTES.PLANS_ADD}
          color={LinkColor.GrayGradient}
          icon={<AddIcon />}
        >
          Add new plan
        </Link>
      }
      // fullHeight
    >
      {!isDataLoading && (
        <div className="flex flex-wrap gap-y-14 gap-x-16">
          {plans.map((plan) => (
            <StatsCard key={plan.id} {...plan} />
          ))}
        </div>
      )}
    </Card>
  );
}
