import * as yup from 'yup';
import { Inputs } from './types';

export const schema = yup
  .object({
    name: yup.string().required('Name is required'),
    dataOfferingId: yup
      .number()
      .typeError('Data is required')
      .required('Data is required'),
    voiceOfferingId: yup
      .number()
      .typeError('Voice is required')
      .required('Voice is required'),
    smsOfferingId: yup
      .number()
      .typeError('Sms is required')
      .required('Sms is required'),
    price: yup.number().moreThan(0).required('Price is required'),
    bundleId: yup
      .number()
      .when(
        ['dataOfferingId', 'smsOfferingId', 'voiceOfferingId'],
        ([dataOfferingId, smsOfferingId, voiceOfferingId], schema) => {
          return !dataOfferingId && !smsOfferingId && !voiceOfferingId
            ? schema.required('Preset is required')
            : schema.nullable();
        }
      ),
    pointsPerMonth: yup
      .string()
      .when('pointsAccrual', ([pointsAccrual], schema) => {
        return pointsAccrual === 'yes'
          ? schema.required('Points amount is required')
          : schema.optional();
      })
  })
  .required() as yup.ObjectSchema<Inputs>;
