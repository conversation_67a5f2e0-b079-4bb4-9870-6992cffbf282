import { PlanDimension, PlanStatus } from 'src/types/plans';
import { Inputs } from './types';

export const planActivationOptions = [
  { value: PlanStatus.Active, label: 'Immediately' },
  { value: PlanStatus.Inactive, label: 'Specific date' }
];

export const dimensions: {
  label: string;
  formField: keyof Inputs;
  type: PlanDimension;
  extension: string;
  limitField: keyof Inputs;
}[] = [
  {
    type: PlanDimension.Data,
    label: 'Data',
    formField: 'dataOfferingId',
    extension: 'GB',
    limitField: 'dataLimit'
  },
  {
    type: PlanDimension.Voice,
    label: 'Voice',
    formField: 'voiceOfferingId',
    extension: 'mins',
    limitField: 'voiceLimit'
  },
  {
    type: PlanDimension.Sms,
    label: 'SMS',
    formField: 'smsOfferingId',
    extension: 'sms',
    limitField: 'smsLimit'
  }
];

export const formDefaultValues = {
  bundleId: null,
  name: 'Nearly unlimited',
  status: PlanStatus.Active,
  implementationDate: '',
  price: 0,
  planKey: 'some-key'
};

export const minPriceDefault = {
  data: 0,
  sms: 0,
  voice: 0,
  total: 0
};

export const tabResetValues = {
  dataOfferingId: undefined,
  smsOfferingId: undefined,
  voiceOfferingId: undefined,
  voiceLimit: undefined,
  smsLimit: undefined,
  dataLimit: undefined,
  bundleId: null
};

export const dimensionMarks = {
  [PlanDimension.Sms]: [100, 500, 1000, 2000, 3000],
  [PlanDimension.Data]: [0, 10, 30, 50, 100, 250],
  [PlanDimension.Voice]: [100, 500, 1000, 2000, 3000, 5000]
};
