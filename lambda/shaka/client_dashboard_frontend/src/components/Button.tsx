import { ReactNode } from 'react';
import Loader from './Loader';
import { ButtonColor, buttonStyles } from './sharedStyles';
import { twMerge } from 'tailwind-merge';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  color?: ButtonColor;
  leftIcon?: ReactNode;
}

export default function Button({
  children,
  isLoading,
  color = ButtonColor.Default,
  className,
  disabled,
  leftIcon,
  ...props
}: Props) {
  const style = twMerge(
    buttonStyles[color],
    disabled && 'pointer-events-none opacity-50'
  );

  return (
    <button
      className={twMerge(
        'relative',
        style,
        className,
        isLoading && 'pointer-events-none'
      )}
      {...props}
    >
      <span
        className={twMerge(
          'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2',
          !isLoading && 'opacity-0'
        )}
      >
        <Loader size={24} />
      </span>
      <span
        className={twMerge(
          'text-center',
          isLoading && 'opacity-0',
          leftIcon && 'flex items-center gap-4'
        )}
      >
        {leftIcon}
        {children}
      </span>
    </button>
  );
}

export { ButtonColor } from './sharedStyles';
