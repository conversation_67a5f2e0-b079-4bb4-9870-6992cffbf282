import { BlockedIcon } from 'src/icons/Blocked';
import Dialog, { ButtonActionColor, DialogActionButton } from './Dialog';

interface Props {
  isOpen: boolean;
  onCancel: () => void;
  onSubmit?: () => void;
  title?: string;
  description?: React.ReactNode;
  cancelButtonText?: string;
  submitButtonText?: string;
  cancelActionColor?: ButtonActionColor;
  submitActionColor?: ButtonActionColor;
  dialogType?: 'warning' | 'question' | 'info';
}

export default function NotificationDialog({
  isOpen,
  onCancel,
  onSubmit,
  description,
  title,
  cancelActionColor = 'success',
  submitActionColor = 'danger',
  cancelButtonText = 'No',
  submitButtonText = 'Yes'
}: Props) {
  return (
    <Dialog
      title={
        <span className="inline-block rounded-full bg-gray-100 p-2">
          <BlockedIcon className="size-5" />
        </span>
      }
      isOpen={isOpen}
      onClose={onCancel}
      leftButton={
        onSubmit && (
          <DialogActionButton
            className="w-full"
            actionColor={submitActionColor}
            onClick={onSubmit}
          >
            {submitButtonText}
          </DialogActionButton>
        )
      }
      rightButton={
        <DialogActionButton
          className="w-full"
          onClick={onCancel}
          actionColor={cancelActionColor}
        >
          {cancelButtonText}
        </DialogActionButton>
      }
      hideCloseButton
      panelStyles="w-4/5 p-8 sm:max-w-sm"
    >
      <h3 className="text-xl font-semibold text-center text-white">{title}</h3>
      <div className="text-[#858585] text-xl mt-4 mb-4">{description}</div>
    </Dialog>
  );
}
