import { ReactNode } from 'react';
import clsx from 'clsx';
import { XMarkIcon } from '@heroicons/react/24/solid';

type Props = {
  className?: string;
  variant: 'success' | 'error' | 'warning';
  children: ReactNode;
  onClose: () => void;
};

const classes = {
  main: {
    success: 'bg-green-200 text-green-900',
    error: 'bg-red-200 text-red-900',
    warning: 'bg-amber-200 text-amber-900'
  },
  button: {
    success: 'hover:bg-green-300',
    error: 'hover:bg-red-300',
    warning: 'hover:bg-amber-300'
  }
};

export default function Alert({
  variant,
  className = '',
  children,
  onClose
}: Props) {
  return (
    <div
      className={clsx(
        classes.main[variant],
        className,
        'flex items-start px-5 py-4 rounded-md'
      )}
    >
      <div className="grow">{children}</div>
      <div
        className={clsx(classes.button[variant], 'hover:cursor-pointer')}
        onClick={onClose}
      >
        <XMarkIcon className="w-5 h-5" />
      </div>
    </div>
  );
}
