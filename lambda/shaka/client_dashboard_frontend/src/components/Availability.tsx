import { Controller, useFormContext } from 'react-hook-form';
import InputWrapper from 'src/components/form/WrapperWithLabel';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import Calendar from 'src/components/form/Calendar';
import { DateTime } from 'luxon';

export const availabilityOptions = [
  { value: 'active', label: 'Immediately' },
  { value: 'inactive', label: 'Specific date' }
];

export default function Availability({ min }: { min?: string }) {
  const {
    control,
    register,
    watch,
    formState: { errors }
  } = useFormContext<{ availability: string; availability_date: string }>();

  const watchAvailability = watch('availability');

  const minDate = (min ? DateTime.fromISO(min) : DateTime.now())
  .toFormat('yyyy-MM-dd hh:mm')
  .replace(' ', 'T');

  return (
    <InputWrapper
      label="Availability date"
      error={errors.availability_date?.message}
    >
      <Controller
        name="availability"
        control={control}
        render={({ field: { value, onChange } }) => (
          <div className="flex items-center gap-8 h-12">
            <RadioButtonGroup
              horizontal
              value={value}
              options={availabilityOptions}
              onChange={onChange}
            />
            {watchAvailability === 'inactive' && (
              <Calendar {...register('availability_date')} min={minDate} />
            )}
          </div>
        )}
      />
    </InputWrapper>
  );
}
