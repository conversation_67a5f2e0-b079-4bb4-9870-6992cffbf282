import { ListboxOption } from 'src/types/common';
import {
  BundleId,
  BundledPlan,
  PlanComponentOffering,
  PlanDimension,
  PlanOfferingSet
} from 'src/types/plans';
import { create } from 'zustand';

type State = PlanOfferingSet & { isLoaded: boolean };

export type DimensionsLimit = { id: number; limit: number };

type PlansOfferingStore = State;

export const usePlansOfferingStore = create<State>(() => ({
  bundled_plan_offerings: [],
  plan_component_offerings: [],
  isLoaded: false
}));

export const selectorPlanComponentOfferingLoaded = (
  state: PlansOfferingStore
) => state.isLoaded;

const getOfferingsByDimension = (
  state: PlansOfferingStore,
  dimension: PlanDimension
) =>
  state.plan_component_offerings.filter(
    (offer) =>
      offer.plan_component.dimension === dimension &&
      !offer.plan_component.bundle_only
  );

const getOfferingOptions = (
  state: PlansOfferingStore,
  dimension: PlanDimension
) =>
  getOfferingsByDimension(state, dimension).reduce((acc, curr) => {
    acc.push({
      value: curr.id,
      label: curr.plan_component.description
    });
    return acc;
  }, [] as ListboxOption<number>[]);

export const selectorDataOfferingsWithLimit = (state: PlansOfferingStore) => {
  return getOfferingOptions(state, PlanDimension.Data);
};

export const selectorSmsOfferingsWithLimit = (state: PlansOfferingStore) =>
  getOfferingOptions(state, PlanDimension.Sms);

export const selectorVoiceOfferingsWithLimit = (state: PlansOfferingStore) =>
  getOfferingOptions(state, PlanDimension.Voice);

const getOfferingLimits = (
  state: PlansOfferingStore,
  dimension: PlanDimension
) =>
  getOfferingsByDimension(state, dimension).reduce((limits, offering) => {
    limits.push({
      id: offering.id,
      limit: offering.plan_component.max_limit
    });

    return limits;
  }, [] as DimensionsLimit[]);

export const selectorOfferingsLimits =
  (dimension: PlanDimension) => (state: PlansOfferingStore) =>
    getOfferingLimits(state, dimension);

export const setPlansOffering = (newState: PlanOfferingSet) =>
  usePlansOfferingStore.setState(() => ({ ...newState, isLoaded: true }));

export const selectorPlanComponentOfferingById = (state: PlansOfferingStore) =>
  state.plan_component_offerings.reduce<Record<number, PlanComponentOffering>>(
    (acc, curr) => {
      acc[curr.id] = curr;

      return acc;
    },
    {}
  );

export const selectorBundledPlanOfferings = (state: PlansOfferingStore) =>
  state.bundled_plan_offerings;

export const selectorBundledPlanOfferingsOptions = (
  state: PlansOfferingStore
) =>
  state.bundled_plan_offerings.map((bundle) => ({
    value: bundle.id,
    label: bundle.description
  }));

export const selectorBundledPlanOfferingsById = (state: PlansOfferingStore) =>
  state.bundled_plan_offerings.reduce<Record<BundleId, BundledPlan>>(
    (acc, curr) => {
      acc[curr.id] = curr;

      return acc;
    },
    {}
  );
