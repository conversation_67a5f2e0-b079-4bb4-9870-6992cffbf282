import { Bolton, BoltonsDetails } from 'src/types/boltons';
import { create } from 'zustand';

type State = { boltonDetails?: BoltonsDetails; isLoaded: boolean };

const useBoltOnsStore = create<State>(() => ({
  boltonDetails: undefined,
  isLoaded: false
}));

const selectorBoltOnsDetails = (state: State) => state.boltonDetails;

const selectorBoltOnById = (state: State) => (id?: string) =>
  state.boltonDetails?.bolt_ons.find((bolton) => bolton.id.toString() == id);

const selectorBoltOnsLoaded = (state: State) => state.isLoaded;

const setBoltOns = (boltonDetails?: BoltonsDetails) =>
  useBoltOnsStore.setState(() => ({ boltonDetails, isLoaded: true }));

const sortByStatus = (a: Bolton, b: Bolton) => {
  if (a.status.toLocaleLowerCase() === 'enabled') return -1;
  if (b.status.toLocaleLowerCase() === 'enabled') return 1;
  return 0;
};

const selectorSortedByStatusBoltOns = (state: State) => {
  return state.boltonDetails?.bolt_ons.sort(sortByStatus) || [];
};

const selectAmountOfActiveBoltOns = (state: State) =>
  selectorSortedByStatusBoltOns(state).filter(
    (bolton) => bolton.status.toLowerCase() === 'enabled'
  ).length;

export {
  useBoltOnsStore,
  selectorBoltOnsDetails,
  selectorBoltOnById,
  selectorBoltOnsLoaded,
  setBoltOns,
  selectorSortedByStatusBoltOns,
  selectAmountOfActiveBoltOns
};
