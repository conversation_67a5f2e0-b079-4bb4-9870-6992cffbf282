import { Navigate, Routes as RouterRoutes, Route } from 'react-router-dom';
import SubscribersView from 'src/views/manage/Subscribers';
import ManageDashboard from 'src/views/manage/Hub';
import PlansView from 'src/views/manage/Plans';
import PlanView from 'src/views/manage/Plans/PlanView';
import NetworkStatistics from 'src/views/manage/NetworkStatistics';
import BrandingView from 'src/views/customize/Branding';
import Users from 'src/views/settings/Users';
import { ROUTES } from 'src/config/navigation';
import UserView from 'src/views/settings/Users/<USER>';
import NetworkName from 'src/views/customize/NetworkName';
import Profile from 'src/views/settings/Profile';
import Beams from 'src/views/engage/Beams';
import SubscriberInfo from 'src/views/manage/Subscribers/SubscriberInfo';
import useAnalytics from 'src/hooks/useAnalytics';
import BeamView from 'src/views/engage/Beams/BeamView';
import BeamDetails from 'src/views/engage/Beams/BeamDetails';
import Campaigns from 'src/views/monetize/Campaigns';
import CampaignView from 'src/views/monetize/Campaigns/CampaignView';
import Perks from 'src/views/monetize/Perks';
import PerkView from 'src/views/monetize/Perks/PerkView';
import PerkDetails from 'src/views/monetize/Perks/PerkDetails';
import useDocumentTitle from 'src/hooks/useDocumentTitle';
import BoltOnView from 'src/views/manage/Plans/BoltOnView';
import Login from 'src/views/login/Login';
import ProtectedRoute from './ProtectedRoute';
import Layout from 'src/layout/Layout';
import ForgotConfirmPassword from 'src/views/login/ForgotConfirmPassword';
import LoginLayout from 'src/layout/LoginLayout';

export default function Routes() {
  useAnalytics();
  useDocumentTitle();

  return (
    <RouterRoutes>
      <Route element={<LoginLayout />}>
        <Route path={ROUTES.LOGIN} element={<Login />} />
        <Route
          path={ROUTES.FORGOT_PASSWORD}
          element={<ForgotConfirmPassword />}
        />
      </Route>

      <Route
        element={
          <ProtectedRoute>
            <Layout />
          </ProtectedRoute>
        }
      >
        {/* Routes for Hub */}
        <Route path={ROUTES.DASHBOARD} element={<ManageDashboard />} />

        {/* Routes for manage */}
        <Route path={ROUTES.SUBSCRIBERS} element={<SubscribersView />} />
        <Route
          path={`${ROUTES.SUBSCRIBERS}/:subscriberId`}
          element={<SubscriberInfo />}
        />
        <Route
          path={ROUTES.ACTIVE_SUBSCRIBERS}
          element={<div>Active subscribers</div>}
        />
        <Route
          path={ROUTES.INACTIVE_SUBSCRIBERS}
          element={<div>Inactive subscribers</div>}
        />
        <Route path={ROUTES.PLANS} element={<PlansView />} />
        <Route path={ROUTES.PLANS_ADD} element={<PlanView />} />
        <Route path={`${ROUTES.PLANS}/:planId`} element={<PlanView />} />
        <Route path={`${ROUTES.BOLT_ON_ADD}`} element={<BoltOnView />} />
        <Route
          path={`${ROUTES.BOLT_ON_EDIT}/:boltonId`}
          element={<BoltOnView />}
        />
        <Route
          path={ROUTES.NETWORK_STATISTICS}
          element={<NetworkStatistics />}
        />
        {/* Routes for branding */}
        <Route path={ROUTES.BRANDING} element={<BrandingView />} />
        <Route path={ROUTES.SPN} element={<NetworkName />} />
        <Route path={ROUTES.VOICE_MAIL} element={<div>Voice mail</div>} />

        {/* Routes for monetize */}
        <Route path={ROUTES.CAMPAIGNS} element={<Campaigns />} />
        <Route path={ROUTES.CAMPAIGNS_ADD} element={<CampaignView />} />
        <Route
          path={`${ROUTES.CAMPAIGNS}/:campaignId`}
          element={<CampaignView />}
        />
        <Route path={ROUTES.PERKS} element={<Perks />} />
        <Route path={ROUTES.PERKS_ADD} element={<PerkView />} />
        <Route path={`${ROUTES.PERKS}/:perkId`} element={<PerkDetails />} />
        <Route path={`${ROUTES.PERKS_EDIT}/:perkId`} element={<PerkView />} />

        {/* Routes for engage */}
        <Route path={ROUTES.BEAMS} element={<Beams />} />
        <Route path={ROUTES.BEAMS_ADD} element={<BeamView />} />
        <Route path={`${ROUTES.BEAMS_EDIT}/:beamId`} element={<BeamView />} />
        <Route path={`${ROUTES.BEAMS}/:beamId`} element={<BeamDetails />} />
        {/* Routes for settings */}
        <Route path={ROUTES.USERS} element={<Users />} />
        <Route path={ROUTES.USER_ADD} element={<UserView />} />
        <Route path={`${ROUTES.USERS}/:userId`} element={<UserView />} />
        <Route path={ROUTES.PROFILE} element={<Profile />} />
        <Route path={ROUTES.CONFIG} element={<div>Config</div>} />
        <Route path={ROUTES.API} element={<div>API</div>} />
        <Route
          path="*"
          element={<Navigate to={ROUTES.DASHBOARD} replace={true} />}
        />
      </Route>
    </RouterRoutes>
  );
}
