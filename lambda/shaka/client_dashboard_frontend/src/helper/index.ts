import { DateTime } from 'luxon';

export const toLocalTime = (date: string) =>
  DateTime.fromISO(date).toFormat('MM/dd/yyyy');

export const formatNumber = (
  value: number,
  options: Intl.NumberFormatOptions = {}
) => {
  const formatter = new Intl.NumberFormat('en-US', options);
  return formatter.format(value);
};

export const currencyFormat = (price: number) =>
  formatNumber(price, {
    style: 'currency',
    currency: 'GBP'
  });

export const currencyRounded = (price: number) =>
  currencyFormat(price).replace('.00', '');

export const numberInStringRounded = (currentNumber: string) =>
  currentNumber?.replace('.00', '');

export const capitalize = (text: string) =>
  text.charAt(0).toUpperCase() + text.slice(1);

export const getInitials = (name?: string, email?: string) => {
  if (!name) {
    if (!email) return 'U';
    return `${email.charAt(0)}${email.charAt(1)}`;
  }

  const [firstName, lastName] = name.split(' ');

  if (!lastName) return firstName.charAt(0);

  return `${firstName.charAt(0)}${lastName.charAt(0)}`;
};

const timeLabels = ['years', 'months', 'days'];
const timeLabelsSingular = ['year', 'month', 'day'];

export const getDateDurationFromNow = (startDate: string) => {
  const end = DateTime.fromJSDate(new Date());
  const start = DateTime.fromISO(startDate);

  const {
    years,
    months,
    days = 0
  } = end.diff(start, ['years', 'months', 'days']).toObject();

  const timePeriods =
    years && years >= 1 ? [years, months, 0] : [0, months, Math.floor(days)];

  const resultValues = timePeriods
    .map(
      (value, index) =>
        value &&
        `${value} ${value > 1 ? timeLabels[index] : timeLabelsSingular[index]}`
    )
    .filter(Boolean);

  return resultValues.join(', ') || 'Less than 1 day';
};

export const getFirstNLetters = (text: string, number: number = 1) => {
  return text.slice(0, number).toUpperCase();
};

export const convertToGb = (data: number | null) => {
  if (!data) {
    return '0';
  }

  const number = Number(data);

  if (number < 1024) {
    return number.toFixed(2) + ' B';
  }

  if (number < 1024 * 1024) {
    const kb = number / 1024;

    return kb.toFixed(2) + ' KB';
  }

  if (number < 1024 * 1024 * 1024) {
    const mb = number / 1024 / 1024;
    return mb.toFixed(2) + ' MB';
  }

  const gb = number / 1024 / 1024 / 1024;
  return gb.toFixed(2) + ' GB';
};

export const convertToMins = (mins?: number | null) => {
  if (!mins) {
    return '0';
  }

  const min = mins / 60;

  return min.toFixed(2);
};

export const getMessageScheduleCountdown = (date: string) => {
  const diff = DateTime.fromISO(date).diffNow();

  const days = diff.as('days');
  const hours = diff.as('hours');
  const minutes = diff.as('minutes');

  if (days >= 1) {
    return `${Math.floor(days)} days`;
  }

  if (hours >= 1) {
    return `${Math.floor(hours)} hours`;
  }

  return `${Math.floor(minutes)} minutes`;
};

export function getSMSUnits(message: string = '') {
  const GSM_7_CHARACTERS =
    '@Δ 0¡P¿p£_!1AQaq$Φ"2BRbr¥Γ#3CScsèΛ¤4DTdtéΩ%5EUeuùΠ&6FVfvìΨ\'7GWgwòΣ(8HXhxÇΘ)9IYiy\nΞ*:JZjzØ+;KÄkäøÆ<LÖlö\ræ-=MÑmñÅß.>NÜnüåÉ/?O§oà';
  const GSM_7_EXTENDED_CHARACTERS = '|^€{}[~]\\';

  function isGSM7Character(char: string) {
    return (
      GSM_7_CHARACTERS.includes(char) ||
      GSM_7_EXTENDED_CHARACTERS.includes(char)
    );
  }

  const isGSM7 = [...message].every(isGSM7Character);

  if (isGSM7) {
    const segmentLength = message.length <= 160 ? 160 : 153;
    return Math.ceil(message.length / segmentLength);
  } else {
    const segmentLength = message.length <= 70 ? 70 : 67;
    return Math.ceil([...message].length / segmentLength);
  }
}

export const toQueryString = (
  obj: { [key: string]: string | number | boolean } = {}
) =>
  '?'.concat(
    Object.keys(obj)
      .map((e) =>
        obj[e] ? `${encodeURIComponent(e)}=${encodeURIComponent(obj[e])}` : null
      )
      .filter((e) => !!e)
      .join('&')
  );

const getNCharactersFromIndex =
  (charAmount: number) =>
  (str: string, startIndex: number = 0) =>
    str.substring(startIndex, startIndex + charAmount);

export const getFiveCharactersFromIndex = getNCharactersFromIndex(5);
