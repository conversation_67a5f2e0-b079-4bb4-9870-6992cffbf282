#!/usr/bin/env python

import os
import json
import sys
from pathlib import Path

def main():
    state_machine_name = sys.argv[1]
    this_file = Path(__file__)
    state_machine_file = this_file.parent.parent / 'nexus' / 'state-machines' / f'{state_machine_name}.json'
    with state_machine_file.open() as f:
        state_machine_definition = f.read()
        json.loads(state_machine_definition)
        for lambda_name in ['plan_change', 'slack_debug']:
            var_name = f'${{{lambda_name}_fn_arn}}'
            arn = f'arn:aws:lambda:eu-west-2:727907215122:function:prod-{lambda_name.replace("_", "-")}'
            state_machine_definition = state_machine_definition.replace(var_name, arn)
        state_machine_defintion = json.dumps(json.loads(state_machine_definition))
        state_machine_definition = state_machine_definition.replace('"', '\\"')
        os.system(f'aws stepfunctions --endpoint-url http://localhost:8083 create-state-machine --definition "{state_machine_definition}" --name {state_machine_name} --role-arn "arn:aws:iam::012345678901:role/DummyRole"')


if __name__ == '__main__':
    main()
