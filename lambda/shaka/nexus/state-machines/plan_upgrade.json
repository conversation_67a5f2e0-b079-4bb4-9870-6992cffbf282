{"Comment": "Plan upgrade - expects a plan change id", "StartAt": "Validate Upgrade Config", "States": {"Validate Upgrade Config": {"Type": "Task", "Next": "<PERSON>", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "validate_and_lock"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Bill Upgrade": {"Type": "Task", "Next": "Wait For Billing Change", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "bill"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Wait For Billing Change": {"Type": "Task", "Next": "Effect Change In Provider", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "check_billing_change"}, "Resource": "${plan_change_fn_arn}", "Retry": [{"ErrorEquals": ["CheckNotReadyError", "LambdaFunctionFailed"], "IntervalSeconds": 60, "MaxAttempts": 5, "BackoffRate": 1}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Effect Change In Provider": {"Type": "Task", "Next": "Wait For Provider Change", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "effect_provider_change"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Wait For Provider Change": {"Type": "Task", "Next": "Complete", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "check_provider_change"}, "Resource": "${plan_change_fn_arn}", "Retry": [{"ErrorEquals": ["CheckNotReadyError", "LambdaFunctionFailed"], "IntervalSeconds": 120, "MaxAttempts": 10, "BackoffRate": 1}], "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Complete": {"Type": "Task", "End": true, "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "complete"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Slack Debug": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"Payload": {"text.$": "States.JsonToString($)"}, "FunctionName": "${slack_debug_fn_arn}"}, "End": true}}}