import time
from datetime import <PERSON><PERSON><PERSON>
from django.test import LiveServerTestCase, override_settings
from django.test.testcases import LiveServerThread
from django.core.servers.basehttp import ThreadedWSGIServer
from django.conf import settings
from django.utils import timezone
from core.tests.factories import PlanChangeFactory, SubscriptionPaymentFactory, SubscriptionFactory
from core.models import PlanChange, SubscriptionPayment
from core.plan_change import PlanChangeValidationError, PlanChangeError
from core.tests.fake_services import FakeServices
from core.tests.scenario import Scenario
from core.billing import prorate_remaining_charge
from core.utils import format_datetime_for_step_functions
from .plan_change import lambda_handler, CheckNotReadyError

def make_wsgi_server_with_reuse(_, *args, **kwargs):
    kwargs['allow_reuse_address'] = True
    return ThreadedWSGIServer(*args, **kwargs)

class LiveServerThreadWithReuse(LiveServerThread):
    server_class = make_wsgi_server_with_reuse

@override_settings(DEBUG=True)
class PlanChangeTestCase(LiveServerTestCase):
    server_thread_class = LiveServerThreadWithReuse
    stripe_backend = 'fake'
    port = 8050
    host = "0.0.0.0"

    def setUp(self):
        super().setUp()
        self.fake_services = FakeServices(stripe_backend=self.stripe_backend)
        self.fake_services.set_up()
        self.scenario = Scenario(self.fake_services)

    def tearDown(self):
        self.fake_services.tear_down()
        super().tearDown()

    def assert_payments(self, subscription, *amounts):
        payments = SubscriptionPayment.objects.filter(subscription=subscription).order_by('id')
        self.assertEqual(len(payments), len(amounts))
        for payment, amount in zip(payments, amounts):
            self.assertEqual(round(payment.amount, 2), round(amount, 2))

    def run_lambda(self, plan_change_id, action, sub_execution_id='sub'):
        if isinstance(plan_change_id, PlanChange):
            plan_change_id = plan_change_id.id
        data = {
            'action': action,
            'plan_change_id': plan_change_id
        }
        if sub_execution_id:
            data['sub_execution_id'] = sub_execution_id
        return lambda_handler(data, None)

    def wait_for(self, fn, timeout=5):
        start = time.time()
        while time.time() - start < timeout:
            if fn():
                return
            time.sleep(0.1)
        raise TimeoutError()


class CancelDowngradePlanChangeTests(PlanChangeTestCase):
    def test_validate_throws_exception_if_plan_is_not_a_cancel(self):
        plan_change = PlanChangeFactory(upgrade=True)
        plan_change.change_type = PlanChange.ChangeType.CANCEL_CHANGE
        plan_change.save()
        with self.assertRaises(PlanChangeValidationError) as cm:
            self.run_lambda(plan_change, 'validate_and_lock')
        self.assertEqual(str(cm.exception), 'Target plan change is not set')

    def test_validate_throws_exception_if_target_change_is_not_cancellable(self):
        plan_change = PlanChangeFactory(cancel_downgrade=True)
        target_change = plan_change.target_plan_change
        target_change.change_type = PlanChange.ChangeType.UPGRADE
        target_change.save()
        with self.assertRaises(PlanChangeValidationError) as cm:
            self.run_lambda(plan_change, 'validate_and_lock')
        self.assertEqual(str(cm.exception), 'Target plan change is not cancellable')

    def test_validate_throws_exception_if_target_change_is_locked(self):
        plan_change = PlanChangeFactory(cancel_downgrade=True)
        target_change = plan_change.target_plan_change
        target_change.status = PlanChange.Status.LOCKED
        target_change.save()
        with self.assertRaises(PlanChangeValidationError) as cm:
            self.run_lambda(plan_change, 'validate_and_lock')
        self.assertEqual(str(cm.exception), 'Target plan change is not cancellable')

    def test_validate_throws_exception_if_target_change_has_different_subscription(self):
        plan_change = PlanChangeFactory(cancel_downgrade=True)
        target_change = plan_change.target_plan_change
        target_change.subscription = SubscriptionFactory(subscriber__client=plan_change.subscription.subscriber.client, using=True)
        target_change.save()
        with self.assertRaises(PlanChangeValidationError) as cm:
            self.run_lambda(plan_change, 'validate_and_lock')
        self.assertEqual(str(cm.exception), 'Target plan change has a different subscription')

    def test_lock_locks_plan_change_and_sets_id(self):
        plan_change = PlanChangeFactory(cancel_downgrade=True)
        assert self.run_lambda(plan_change, 'validate_and_lock', 'sub_123') is not None
        plan_change.refresh_from_db()
        self.assertEqual(plan_change.status, PlanChange.Status.LOCKED)
        self.assertEqual(plan_change.sub_execution_id, 'sub_123')

    def test_lock_locks_target_plan_change_and_sets_id(self):
        plan_change = PlanChangeFactory(cancel_downgrade=True)
        assert self.run_lambda(plan_change, 'validate_and_lock', 'sub_123') is not None
        plan_change.refresh_from_db()
        self.assertEqual(plan_change.target_plan_change.status, PlanChange.Status.LOCKED)

    def test_bill_does_nothing_if_nothing_to_do(self):
        stripe = self.fake_services.stripe
        subscription = self.scenario.setup_subscription_with_billing('2024-01-15')
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription)
        source_plan = subscription.latest_plan
        target_plan = plan_change.target_plan
        target_plan.sync_to_gateway_if_necessary()
        expected_initial_charge = prorate_remaining_charge(
            source_plan.price,
            stripe.current_billing_cycle_start,
            stripe.current_billing_cycle_end,
            stripe.current_time
        )
        stripe.advance_to_middle_of_next_billing_cycle()
        stripe.advance_time_slightly()
        cancelling_plan_change = PlanChangeFactory(cancel_downgrade=True, target_plan_change=plan_change, locked=True)
        assert self.run_lambda(cancelling_plan_change, 'bill')
        stripe.advance_to_start_of_next_billing_cycle()
        stripe.advance_time_past_collection()
        self.assert_payments(subscription, expected_initial_charge, source_plan.price, source_plan.price)

    def test_complete_sets_target_plan_change_to_cancelled_and_this_one_to_completed(self):
        plan_change = PlanChangeFactory(cancel_downgrade=True, locked=True)
        target_change = plan_change.target_plan_change
        target_change.status = PlanChange.Status.LOCKED
        target_change.save()
        assert self.run_lambda(plan_change, 'complete') is not None
        plan_change.refresh_from_db()
        target_change.refresh_from_db()
        assert plan_change.status == PlanChange.Status.COMPLETE
        assert target_change.status == PlanChange.Status.CANCELLED


class DowngradePlanChangeTests(PlanChangeTestCase):
    def test_validate_throws_exception_if_plan_is_not_a_downgrade(self):
        plan_change = PlanChangeFactory(upgrade=True)
        plan_change.change_type = PlanChange.ChangeType.DOWNGRADE
        plan_change.save()
        target_plan = plan_change.target_plan
        subscription = plan_change.subscription
        assert not target_plan.is_upgrade_if_going_to(subscription.latest_plan)
        assert subscription.latest_plan.is_upgrade_if_going_to(target_plan)
        with self.assertRaises(PlanChangeValidationError) as cm:
            self.run_lambda(plan_change, 'validate')
        self.assertEqual(str(cm.exception), 'Target plan is not a downgrade')

    def test_lock_locks_plan_change_and_sets_id(self):
        plan_change = PlanChangeFactory(downgrade=True)
        assert self.run_lambda(plan_change, 'validate_and_lock', 'sub_123') is not None
        plan_change.refresh_from_db()
        self.assertEqual(plan_change.status, PlanChange.Status.LOCKED)
        self.assertEqual(plan_change.sub_execution_id, 'sub_123')

    def test_validate_includes_execution_timestamp(self):
        plan_change = PlanChangeFactory(downgrade=True)
        subscription = plan_change.subscription
        result = self.run_lambda(plan_change, 'validate')
        self.assertEqual(result['future_execution_timestamp'], format_datetime_for_step_functions(subscription.next_billing_cycle_start - timedelta(hours=settings.BILLING_CYCLE_FREEZE_HOURS)))

    def test_validate_includes_completion_expected_timestamp(self):
        plan_change = PlanChangeFactory(downgrade=True)
        subscription = plan_change.subscription
        plan_change.execution_start_time = timezone.now()
        plan_change.save()
        result = self.run_lambda(plan_change, 'validate')
        self.assertEqual(result['completion_expected_timestamp'], format_datetime_for_step_functions(subscription.next_billing_cycle_start + timedelta(hours=settings.BILLING_CYCLE_EXPECT_PAYMENT_AFTER_HOURS)))

    def test_validate_fails_if_wrong_execution_id(self):
        plan_change = PlanChangeFactory(downgrade=True, sub_execution_id='sub_123')
        with self.assertRaises(PlanChangeValidationError) as cm:
            self.run_lambda(plan_change, 'validate', sub_execution_id='sub_456')
        self.assertEqual(str(cm.exception), 'Sub execution id does not match')

    def test_validate_passes_if_same_execution_id(self):
        plan_change = PlanChangeFactory(downgrade=True, sub_execution_id='sub_123')
        assert self.run_lambda(plan_change, 'validate', sub_execution_id='sub_123')

    def test_validate_and_lock_does_not_lock_if_cancelled(self):
        plan_change = PlanChangeFactory(downgrade=True, cancelled=True)
        result = self.run_lambda(plan_change, 'validate_and_lock')
        plan_change.refresh_from_db()
        self.assertEqual(plan_change.status, PlanChange.Status.CANCELLED)
        assert result['status'] == 'cancelled'

    def test_validate_and_lock_has_plan_status(self):
        plan_change = PlanChangeFactory(downgrade=True)
        result = self.run_lambda(plan_change, 'validate_and_lock')
        plan_change.refresh_from_db()
        self.assertEqual(plan_change.status, PlanChange.Status.LOCKED)
        assert result['status'] == 'locked'

    def test_bill_errors_if_cancelled(self):
        subscription = self.scenario.setup_subscription_with_billing('2024-01-15')
        plan_change = PlanChangeFactory(downgrade=True, cancelled=True, subscription=subscription)
        self.run_lambda(plan_change, 'validate_and_lock')
        plan_change.refresh_from_db()
        with self.assertRaises(PlanChangeError) as cm:
            self.run_lambda(plan_change, 'bill')
        self.assertEqual(str(cm.exception), 'Plan change is cancelled')

    def test_bill_sets_up_stripe_schedule_to_downgrade(self):
        stripe = self.fake_services.stripe
        subscription = self.scenario.setup_subscription_with_billing('2024-01-15')
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription)
        source_plan = subscription.latest_plan
        target_plan = plan_change.target_plan
        assert target_plan.price < source_plan.price
        assert not source_plan.is_upgrade_if_going_to(target_plan)
        target_plan.sync_to_gateway_if_necessary()
        expected_initial_charge = prorate_remaining_charge(
            source_plan.price,
            stripe.current_billing_cycle_start,
            stripe.current_billing_cycle_end,
            stripe.current_time
        )
        stripe.advance_to_middle_of_next_billing_cycle()
        expected_charge = target_plan.price
        assert self.run_lambda(plan_change, 'bill')
        plan_change.refresh_from_db()
        stripe.advance_to_start_of_next_billing_cycle()
        assert stripe.price_is_scheduled_for_next_billing_cycle(subscription, expected_charge)
        invoice_id = stripe.find_invoice(subscription, expected_charge)
        assert invoice_id
        stripe.advance_time_past_collection()
        assert stripe.invoice_collected(invoice_id)
        self.wait_for(lambda: SubscriptionPayment.objects.filter(billing_invoice_id=invoice_id, subscription=subscription, amount=round(expected_charge,2)).exists(), 20)
        stripe.advance_to_start_of_next_billing_cycle()
        stripe.advance_time_past_collection()
        self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=subscription, amount=round(expected_charge,2)).count() == 2, 20)
        self.assert_payments(subscription, expected_initial_charge, source_plan.price, target_plan.price, target_plan.price)

    def test_check_billing_change_fails_if_no_schedule(self):
        subscription = self.scenario.setup_subscription_with_billing('2024-01-15')
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription)
        with self.assertRaises(CheckNotReadyError):
            self.run_lambda(plan_change, 'check_billing_change')

    def test_check_billing_change_succeeds_if_schedule_exists(self):
        stripe = self.fake_services.stripe
        subscription = self.scenario.setup_subscription_with_billing('2024-01-15')
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription)
        target_plan = plan_change.target_plan
        target_plan.sync_to_gateway_if_necessary()
        stripe.advance_time_slightly()
        expected_charge = target_plan.price
        assert self.run_lambda(plan_change, 'bill')
        assert self.run_lambda(plan_change, 'check_billing_change')
        stripe.advance_to_start_of_next_billing_cycle()
        assert stripe.price_is_scheduled_for_next_billing_cycle(subscription, expected_charge)

    def test_check_billing_change_succeeds_if_is_after_completion_expected_and_payment_exists(self):
        stripe = self.fake_services.stripe
        subscription = self.scenario.setup_subscription_with_billing('2024-01-15')
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription, execution_start_time=stripe.current_time)
        plan_change.target_plan.sync_to_gateway_if_necessary()
        stripe.advance_to_start_of_next_billing_cycle()
        stripe.advance_time_past_collection()
        assert self.run_lambda(plan_change, 'bill')
        stripe.advance_to_start_of_next_billing_cycle()
        stripe.advance_time_past_collection()
        assert self.run_lambda(plan_change, 'check_billing_change')

    def test_check_billing_change_fails_if_is_after_completion_expected_and_payment_does_not_exist(self):
        stripe = self.fake_services.stripe
        subscription = self.scenario.setup_subscription_with_billing('2024-01-15')
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription, execution_start_time=stripe.current_time)
        plan_change.target_plan.sync_to_gateway_if_necessary()
        stripe.advance_to_start_of_next_billing_cycle()
        stripe.advance_time_past_collection()
        assert self.run_lambda(plan_change, 'bill')
        stripe.advance_to_start_of_next_billing_cycle()
        stripe.advance_time_past_collection()
        SubscriptionPayment.objects.filter(subscription=subscription, amount=plan_change.target_plan.price).delete()
        with self.assertRaises(CheckNotReadyError):
            self.run_lambda(plan_change, 'check_billing_change')

    def test_effect_provider_change_sends_provider_action_to_downgrade(self):
        subscription = self.scenario.setup_subscription_with_billing()
        self.fake_services.stripe.advance_time_slightly()
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription)
        target_plan_code = plan_change.target_plan.provider_plan_code
        telco = self.fake_services.telco
        assert telco.sim_is_on_plan(subscription.latest_sim.serial_number, subscription.latest_plan.provider_plan_code)
        assert not telco.sim_is_on_plan(subscription.latest_sim.serial_number, target_plan_code)
        assert self.run_lambda(plan_change, 'effect_provider_change') is not None
        assert self.fake_services.cdr_db.plan_change_requested(subscription.latest_sim.serial_number, target_plan_code)
        assert telco.sim_is_on_plan(subscription.latest_sim.serial_number, target_plan_code)
        subscription.refresh_from_db()
        assert subscription.latest_plan == plan_change.target_plan

    def test_wait_for_provider_change_fails_if_not_on_target_plan(self):
        subscription = self.scenario.setup_subscription_with_billing()
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription)
        assert subscription.latest_plan != plan_change.target_plan
        with self.assertRaises(CheckNotReadyError):
            self.run_lambda(plan_change, 'check_provider_change')

    def test_wait_for_provider_change_succeeds_if_on_target_plan(self):
        subscription = self.scenario.setup_subscription_with_billing()
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription)
        self.fake_services.stripe.advance_time_slightly()
        subscription.latest_sim.immediately_move_to_new_plan(plan_change.target_plan)
        assert self.run_lambda(plan_change, 'check_provider_change') is not None

    def test_complete_marks_plan_change_as_complete_and_unlocks(self):
        subscription = self.scenario.setup_subscription_with_billing()
        plan_change = PlanChangeFactory(downgrade=True, locked=True, subscription=subscription)
        subscription.latest_sim.immediately_move_to_new_plan(plan_change.target_plan)
        assert self.run_lambda(plan_change, 'complete') is not None
        plan_change.refresh_from_db()
        assert plan_change.status == PlanChange.Status.COMPLETE


class UpgradePlanChangeTests(PlanChangeTestCase):
    def test_validate_throws_exception_if_plan_is_not_an_upgrade(self):
        plan_change = PlanChangeFactory(downgrade=True)
        plan_change.change_type = PlanChange.ChangeType.UPGRADE
        plan_change.save()
        target_plan = plan_change.target_plan
        subscription = plan_change.subscription
        assert target_plan.is_upgrade_if_going_to(subscription.latest_plan)
        assert not subscription.latest_plan.is_upgrade_if_going_to(target_plan)
        with self.assertRaises(PlanChangeValidationError) as cm:
            self.run_lambda(plan_change, 'validate_and_lock')
        self.assertEqual(str(cm.exception), 'Target plan is not an upgrade')

    def test_lock_locks_plan_change_and_sets_id(self):
        plan_change = PlanChangeFactory(upgrade=True)
        assert self.run_lambda(plan_change, 'validate_and_lock', 'sub_123') is not None
        plan_change.refresh_from_db()
        self.assertEqual(plan_change.status, PlanChange.Status.LOCKED)
        self.assertEqual(plan_change.sub_execution_id, 'sub_123')

    def test_bill_creates_prorated_invoice(self):
        stripe = self.fake_services.stripe
        subscription = self.scenario.setup_subscription_with_billing('2024-01-15')
        source_plan = subscription.latest_plan
        plan_change = PlanChangeFactory(upgrade=True, locked=True, subscription=subscription)
        target_plan = plan_change.target_plan
        assert source_plan.is_upgrade_if_going_to(target_plan)
        target_plan.sync_to_gateway_if_necessary()
        stripe.advance_to_middle_of_next_billing_cycle()
        old_plan_credit = prorate_remaining_charge(
            source_plan.price,
            stripe.current_billing_cycle_start,
            stripe.current_billing_cycle_end,
            stripe.current_time
        )
        new_plan_additional_charge = prorate_remaining_charge(
            target_plan.price,
            stripe.current_billing_cycle_start,
            stripe.current_billing_cycle_end,
            stripe.current_time
        )
        expected_proration = new_plan_additional_charge - old_plan_credit
        assert self.run_lambda(plan_change, 'bill') is not None
        invoice_id = stripe.find_invoice(subscription, expected_proration)
        assert invoice_id
        assert stripe.invoice_finalised(invoice_id)
        plan_change.refresh_from_db()
        assert invoice_id == plan_change.billing_reference
        stripe.advance_time_past_collection()
        assert stripe.invoice_collected(invoice_id)
        self.wait_for(lambda: SubscriptionPayment.objects.filter(billing_invoice_id=invoice_id, subscription=subscription, amount=round(expected_proration,2)).exists(), 20)

    def test_check_billing_change_fails_if_no_payment(self):
        subscription = self.scenario.setup_subscription_with_billing()
        plan_change = PlanChangeFactory(upgrade=True, locked=True, subscription=subscription, billing_reference='123')
        with self.assertRaises(CheckNotReadyError):
            self.run_lambda(plan_change, 'check_billing_change')

    def test_check_billing_change_succeeds_if_payment(self):
        subscription = self.scenario.setup_subscription_with_billing()
        plan_change = PlanChangeFactory(upgrade=True, locked=True, subscription=subscription, billing_reference='123')
        SubscriptionPaymentFactory(billing_invoice_id=plan_change.billing_reference, subscription=subscription)
        assert self.run_lambda(plan_change, 'check_billing_change') is not None

    def test_effect_provider_change_sends_provider_action_to_upgrade(self):
        subscription = self.scenario.setup_subscription_with_billing()
        self.fake_services.stripe.advance_time_slightly()
        plan_change = PlanChangeFactory(upgrade=True, locked=True, subscription=subscription)
        target_plan_code = plan_change.target_plan.provider_plan_code
        telco = self.fake_services.telco
        assert telco.sim_is_on_plan(subscription.latest_sim.serial_number, subscription.latest_plan.provider_plan_code)
        assert not telco.sim_is_on_plan(subscription.latest_sim.serial_number, target_plan_code)
        assert self.run_lambda(plan_change, 'effect_provider_change') is not None
        assert self.fake_services.cdr_db.plan_change_requested(subscription.latest_sim.serial_number, target_plan_code)
        assert telco.sim_is_on_plan(subscription.latest_sim.serial_number, target_plan_code)
        subscription.refresh_from_db()
        assert subscription.latest_plan == plan_change.target_plan

    def test_wait_for_provider_change_fails_if_not_on_target_plan(self):
        subscription = self.scenario.setup_subscription_with_billing()
        plan_change = PlanChangeFactory(upgrade=True, locked=True, subscription=subscription)
        assert subscription.latest_plan != plan_change.target_plan
        with self.assertRaises(CheckNotReadyError):
            self.run_lambda(plan_change, 'check_provider_change')

    def test_wait_for_provider_change_succeeds_if_on_target_plan(self):
        subscription = self.scenario.setup_subscription_with_billing()
        plan_change = PlanChangeFactory(upgrade=True, locked=True, subscription=subscription)
        self.fake_services.stripe.advance_time_slightly()
        subscription.latest_sim.immediately_move_to_new_plan(plan_change.target_plan)
        assert self.run_lambda(plan_change, 'check_provider_change') is not None

    def test_complete_marks_plan_change_as_complete_and_unlocks(self):
        subscription = self.scenario.setup_subscription_with_billing()
        plan_change = PlanChangeFactory(upgrade=True, locked=True, subscription=subscription)
        subscription.latest_sim.immediately_move_to_new_plan(plan_change.target_plan)
        assert self.run_lambda(plan_change, 'complete') is not None
        plan_change.refresh_from_db()
        assert plan_change.status == PlanChange.Status.COMPLETE


class GeneralPlanChangeTests(PlanChangeTestCase):
    def test_lambda_throws_error_if_no_plan_id(self):
        with self.assertRaises(PlanChange.DoesNotExist):
            self.run_lambda(None, 'validate_and_lock')

    def test_lambda_throws_error_if_invalid_plan_id(self):
        with self.assertRaises(PlanChange.DoesNotExist):
            self.run_lambda(123, 'validate_and_lock')

    def test_lambda_throws_error_if_no_sub_execution_id(self):
        with self.assertRaises(KeyError):
            self.run_lambda(PlanChangeFactory().id, 'validate_and_lock', sub_execution_id=None)

    def test_lambda_throws_error_if_invalid_action(self):
        with self.assertRaises(KeyError):
            self.run_lambda(PlanChangeFactory().id, 'invalid_action')

    def test_lock_throws_exception_if_plan_change_is_already_locked(self):
        plan_change = PlanChangeFactory(status=PlanChange.Status.LOCKED)
        with self.assertRaises(PlanChangeValidationError):
            self.run_lambda(plan_change, 'validate_and_lock')

    def test_lock_throws_exception_if_sub_execution_already_exists(self):
        plan_change = PlanChangeFactory(sub_execution_id='sub')
        with self.assertRaises(PlanChangeValidationError):
            self.run_lambda(plan_change, 'validate_and_lock')
