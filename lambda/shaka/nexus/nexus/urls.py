"""
URL configuration for nexus project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import include, path
from simp.views import OnboardingDetailsView, EsimQRView, SimpNotificationsView, DrawListView, DrawDetailView, SpinPrizeView
from simp.urls import router

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('core.urls')),
    path('s/api/v1/<str:client_id>/data/simp-notifications/', SimpNotificationsView.as_view(), name='simp-notifications'),  # simp hack
    path('s/api/v1/<str:client_id>/data/onboarding/', OnboardingDetailsView.as_view(), name='onboarding-detail'),  # simp hack
    path('s/api/v1/<str:client_id>/draws/', DrawListView.as_view(), name='draw-list'),
    path('s/api/v1/<str:client_id>/draws/<int:pk>/', DrawDetailView.as_view(), name='draw-detail'),
    path('s/api/v1/<str:client_id>/draws/<int:pk>/spin/', SpinPrizeView.as_view(), name='draw-spin'),

    path('s/api/v1/<str:client_id>/data/esim-qr/', EsimQRView.as_view(), name='esim-qr'),  # simp hack
    path('s/api/v1/<str:client_id>/', include(router.urls)),  # simp hack
    path('s/', include('subscriber_app.urls')),
    path('simp/', include('simp.urls')),
]
