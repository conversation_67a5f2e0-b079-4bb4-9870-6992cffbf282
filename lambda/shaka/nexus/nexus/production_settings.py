from .settings import *  # pylint: disable=wildcard-import, unused-wildcard-import

DEBUG = False

ALLOWED_HOSTS = ['*']  # WAF does host matching, this is mostly because the EB health check hits only the IP
SITE_URL = 'https://nexus.shaka.tel'
CSRF_TRUSTED_ORIGINS = [SITE_URL]

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/django',
            'formatter': 'simple',
        },
    },
    'formatters': {
        'simple': {
            'format': '%(levelname)s %(asctime)s %(name)s.%(funcName)s:%(lineno)s- %(message)s'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        '': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        }
    },
}

SIX_REFDATA = {
    'CLIENT_ID': '4',
    '100GB_PLAN_ID': '5',
    '30GB_PLAN_ID': '6'
}

BETA_REFDATA = {
    'CLIENT_ID': '5'
}

YAYZI_REFDATA = {
    'CLIENT_ID': '6'
}

IGNORABLE_CLIENTS = ['1','2','3','4']
INOVO_BETA_HACK = True

SLACK_CLIENT_PK_CUTOFF = 4
