# Generated by Django 4.2.7 on 2025-04-17 09:45

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0163_merge_20250318_1542'),
        ('simp', '0010_alter_alert_message'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.Char<PERSON>ield(max_length=255)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('seen', models.BooleanField(default=False)),
                ('subscriber', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='core.subscriber')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
