# Generated by Django 4.2.7 on 2024-11-19 15:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0136_remove_plan_perk_points_per_period'),
    ]

    operations = [
        migrations.CreateModel(
            name='Draw',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('start_datetime', models.DateTimeField()),
                ('end_datetime', models.DateTimeField()),
                ('base_pool_size', models.IntegerField(help_text='Excluding new subscriber buffer and respin buffer')),
                ('new_subscriber_buffer', models.IntegerField(default=0, help_text='Number of prizes to reserve for new subscribers')),
                ('respin_buffer', models.IntegerField(default=0, help_text='Max number of respins to allow')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('running', 'Running'), ('finished', 'Finished')], default='draft', max_length=10)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
            ],
        ),
        migrations.CreateModel(
            name='Prize',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('claimed_by', models.CharField(blank=True, db_index=True, max_length=20, null=True)),
                ('datetime_claimed', models.DateTimeField(blank=True, null=True)),
                ('is_empty', models.BooleanField(db_index=True, default=False)),
                ('draw', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='simp.draw')),
            ],
            options={
                'ordering': ['-id'],
                'indexes': [models.Index(fields=['draw', 'claimed_by'], name='simp_prize_draw_id_056025_idx'), models.Index(fields=['draw', 'is_empty'], name='simp_prize_draw_id_54df68_idx')],
            },
        ),
    ]
