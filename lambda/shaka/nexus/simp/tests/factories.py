from datetime import timedelta
import factory
from django.utils import timezone
from simp.models import Alert, AlertTarget, Notification
from core.tests.factories import ClientFactory, SubscriptionFactory, PlanChangeFactory, PlanFactory, SubscriberFactory
from core.models import PlanChange

class AlertFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Alert

    client = factory.SubFactory(ClientFactory)
    title = factory.Faker('sentence')
    message = factory.Faker('paragraph')
    enabled = True
    alert_level = Alert.AlertLevel.RED


class AlertTargetFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = AlertTarget

    alert = factory.SubFactory(AlertFactory)
    subscriber = factory.SubFactory(SubscriberFactory)

class NotificationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Notification
    subscriber = factory.SubFactory(SubscriberFactory)
    text = factory.Faker('sentence')
    created_at = factory.LazyFunction(timezone.now)
    seen = False

    class Params:
        already_seen = factory.Trait(
            seen=True
        )
