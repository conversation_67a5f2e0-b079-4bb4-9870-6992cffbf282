from django.urls import path, include
from rest_framework import routers
from .views import (
    SetPrizesView, DrawDetailView, DrawListView, SpinPrizeView,
    ManualDrawDetailView, serve_static_file, AlertViewSet,
    SimpNotificationsView
)
router = routers.DefaultRouter()
router.register(r'alerts', AlertViewSet, basename='alert')

urlpatterns = [
    path('draws/<int:draw_id>/set-prizes/', SetPrizesView.as_view(), name='set_prizes'),
    path('draws/<int:draw_id>/', ManualDrawDetailView.as_view(), name='draw_detail'),
    path('api/v0/<str:client_id>/draws/', DrawListView.as_view(), name='draw-list'),
    path('api/v0/<str:client_id>/draws/<int:pk>/', DrawDetailView.as_view(), name='draw-detail'),
    path('api/v0/<str:client_id>/draws/<int:pk>/spin/', SpinPrizeView.as_view(), name='draw-spin'),
    path('static-files/<str:filename>/', serve_static_file, name='serve_static_file'),
    path('api/v0/<str:client_id>/', include(router.urls)),
    path('api/v0/<str:client_id>/notifications/', SimpNotificationsView.as_view(), name='notifications-list'),
    path('api/v0/<str:client_id>/notifications/<int:notification_id>/', SimpNotificationsView.as_view(), name='notifications-detail'),
]
