from urllib.parse import urlencode
import datetime
import os
import hashlib
import jwt as pyjwt
import requests
from authlib.integrations.requests_client import OAuth2Session
from authlib.common.security import generate_token
from authlib.oidc.core import CodeIDToken
from authlib.jose import jwt


def _generate_state(plaintext_token, serverside_secret):
    state_input = plaintext_token + serverside_secret
    state_hash = hashlib.sha256(state_input.encode('utf-8')).hexdigest()
    return state_hash

def _verify_state(state_param, plaintext_token, serverside_secret):
    expected_state = _generate_state(plaintext_token, serverside_secret)
    return state_param == expected_state

def generate_oauth_login_details(client):
    client_id = client.openid_client_id
    client_secret = client.openid_client_secret
    redirect_uri = client.openid_redirect_uri
    scope = client.openid_scopes
    state_plaintext = os.urandom(16).hex()
    state_param = _generate_state(state_plaintext, client.openid_state_secret)

    metadata = requests.get(f'{client.openid_issuer_url}/.well-known/openid-configuration', timeout=10).json()
    session = OAuth2Session(client_id, client_secret, scope=scope, code_challenge_method='S256')
    code_verifier = generate_token(48)

    auth_url = session.create_authorization_url(metadata.get('authorization_endpoint'), redirect_uri=redirect_uri, state=state_param, code_verifier=code_verifier)[0]

#    print(f'./manage.py shell -c\'from core.models import *;from subscriber_app.subscriber_auth import *;print(get_or_create_openid_subscriber(Client.objects.get(pk=5), "", "{state_param}", "{state_plaintext}", "{code_verifier}"))\'')

    return {'auth_url': auth_url, 'state_plaintext': state_plaintext, 'code_verifier': code_verifier}


def get_openid_user_params(client, auth_code, state, state_plaintext, code_verifier):
    if not _verify_state(state, state_plaintext, client.openid_state_secret):
        raise ValueError('Invalid state parameter')
    client_id = client.openid_client_id
    client_secret = client.openid_client_secret
    scope = client.openid_scopes

    metadata = requests.get(f'{client.openid_issuer_url}/.well-known/openid-configuration', timeout=10).json()
    session = OAuth2Session(client_id, client_secret, scope=scope, code_challenge_method='S256')

    response = session.fetch_token(metadata.get('token_endpoint'), authorization_response=f'https://ugh/?code={auth_code}&state={state}', code_verifier=code_verifier, scope=scope, auth=(client_id, client_secret), redirect_uri=client.openid_redirect_uri)
    id_token = response['id_token']
    claims = verify_jwt(client, id_token)

    return {
        'email': claims['email'],
        'name': claims.get('name') or claims.get('profile', {}).get('name', ''),
        'username': claims['sub']
    }

def verify_jwt(client, jwt_token):
    metadata = requests.get(f'{client.openid_issuer_url}/.well-known/openid-configuration', timeout=10).json()
    key_url = metadata['jwks_uri']
    resp = requests.get(key_url, timeout=10)
    resp.raise_for_status()
    keys = resp.json()['keys']

    claims = jwt.decode(jwt_token, keys, claims_cls=CodeIDToken)
    return claims

ACCESS_TOKEN_EXPIRATION = 15
ID_TOKEN_EXPIRATION = 60
REFRESH_TOKEN_EXPIRATION = 30 * 24 * 60

def generate_fresh_auth_tokens(subscriber):
    username = subscriber.cognito_username
    client = subscriber.client
    payload = {
        "sub": username,
        "username": username,
        "cognito:username": username,
        "exp": datetime.datetime.utcnow() + datetime.timedelta(minutes=ACCESS_TOKEN_EXPIRATION),
        "iat": datetime.datetime.utcnow(),
    }
    access_token = pyjwt.encode(payload, client.openid_state_secret, algorithm="HS256")
    payload['exp'] = datetime.datetime.utcnow() + datetime.timedelta(minutes=ID_TOKEN_EXPIRATION)
    id_token = pyjwt.encode(payload, client.openid_state_secret, algorithm="HS256")
    payload['exp'] = datetime.datetime.utcnow() + datetime.timedelta(minutes=REFRESH_TOKEN_EXPIRATION)
    refresh_token = pyjwt.encode(payload, client.openid_state_secret, algorithm="HS256")
    return {'access_token': access_token, 'id_token': id_token, 'refresh_token': refresh_token}

def verify_our_custom_jwt_for_openid(jwt_token, client):
    decoded = pyjwt.decode(jwt_token, client.openid_state_secret, algorithms=["HS256"])
    return decoded

def refresh_our_custom_jwt_for_openid(jwt_token, subscriber, client):
    verify_our_custom_jwt_for_openid(jwt_token, client)
    return generate_fresh_auth_tokens(subscriber)


def get_tokens_from_cognito_code(client, code):
    client_id = client.subscriber_user_pool_client_id
    client_secret = client.subscriber_user_pool_client_secret
    redirect_uri = f'{client.subscriber_webapp_url}/auth'

    token_url = f'{client.subscriber_user_pool_domain}/oauth2/token'

    data = {
        'grant_type': 'authorization_code',
        'client_id': client_id,
        'code': code,
        'redirect_uri': redirect_uri
    }

    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    auth = (client_id, client_secret)

    response = requests.post(token_url, data=urlencode(data), headers=headers, auth=auth, timeout=10)
    tokens = response.json()

    return tokens
