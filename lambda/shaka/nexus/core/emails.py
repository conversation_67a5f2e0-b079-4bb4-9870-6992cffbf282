from datetime import timed<PERSON>ta
import time
import logging
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from django.db import transaction
from django.utils import timezone
from django.template.loader import render_to_string
from core.time_control import STRIPE_BILLING_TIME_CONTROL
from .models import SystemEmail, EmailType, EmailConfiguration, Subscriber, Subscription
from .sig import generate_code


logger = logging.getLogger()
logger.setLevel(logging.INFO)


def create_pending_email(recipient, recipient_key, email_type, client, params):
    try:
        email = SystemEmail.objects.create(
            recipient=recipient,
            recipient_key=recipient_key,
            email_type=EmailType.objects.get(name=email_type),
            client=client,
            status=SystemEmail.EmailStatus.PENDING,
            params=params
        )
        return email
    except EmailType.DoesNotExist:
        logger.error("Email type does not exist: %s", email_type)
        print(f"Email type does not exist: {email_type}")
        return None
    except Exception as e: # pylint: disable=broad-except
        logger.exception("Failed to create email: %s", e)
        return None

def send_pending_email(email):
    if email.status != SystemEmail.EmailStatus.PENDING:
        logger.error("Email is not in pending state.")
        return False

    try:
        locked_email = None
        with transaction.atomic():
            # Lock the email record and confirm it's still pending
            locked_email = SystemEmail.objects.select_for_update(skip_locked=True).filter(
                pk=email.pk,
                recipient_key=email.recipient_key,
                status=SystemEmail.EmailStatus.PENDING
            ).first()

            if not locked_email:
                logger.info("Email either locked or not in pending state anymore.")
                return False
            locked_email.status = SystemEmail.EmailStatus.SENDING
            locked_email.save()

        # Attempt to send the email; errors will be caught by the exception handler
        _actually_send_email(locked_email)
        locked_email.status = SystemEmail.EmailStatus.SENT
        locked_email.sent_at = timezone.now()
        locked_email.save()
        return True

    except Exception as e: # pylint: disable=broad-except
        if locked_email:
            locked_email.status = SystemEmail.EmailStatus.ERRORED
            locked_email.save()
            logger.exception("Failed to send email due to an exception: %s", e)
        else:
            logger.exception("Exception occurred while sending email and no email was locked: %s", e)
        return False

def create_and_send_email(recipient, recipient_key, email_type, client, params):
    email = create_pending_email(recipient, recipient_key, email_type, client, params)
    if email:
        return send_pending_email(email)
    return False


def _actually_send_email(email):
    client = {
        EmailConfiguration.EmailProvider.FAKE: FakeEmailClient,
        EmailConfiguration.EmailProvider.SMTP: SMTPClient,
    }[email.client.email_config.provider]()
    client.send(email)
    return True


class EmailClient:
    def send(self, email):
        raise NotImplementedError("Subclasses must implement this method")

    def get_template(self, email_type):
        return {
            'verify-subscriber': 'emails/verify-subscriber.html',
            'forgot-password': 'emails/forgot-password.html',
            'welcome-new-customer' : 'emails/welcome-new-customer.html',
            'no-payment': 'emails/no-payment.html',
            'esim-activation': 'emails/e-sim-activation.html',
            'self-activation': 'emails/self-activation.html',
            'physical-delivery':'emails/physical-delivery.html',
            'port-number': 'emails/port-number.html',
            'porting-complete': 'emails/porting-complete.html',
            'porting-failed': 'emails/porting-failed.html',
            'voucher-details': 'emails/voucher-details.html',
            'payment-failed': 'emails/payment-failed.html',
            'cancellation': 'emails/cancellation.html',
            'cancellation-pac': 'emails/cancellation-pac.html',
            'cancellation-stac': 'emails/cancellation-stac.html',
            'password-changed': 'emails/password-changed.html',
        }[email_type.name]

    def get_template_params(self, email):
        config = email.client.email_config
        return {
            'client_name': config.client_name or email.client.name,
            'client_mobile_name': config.client_mobile_name or config.client_name or email.client.name,
            'client_footer': config.client_footer,
            'support_email': config.support_email,
            'support_link': config.support_link,
            'help_esim_install_link': config.help_esim_install_link,
            'help_keep_number_link': config.help_keep_number_link,
            'help_data_issue': config.help_data_issue,
            **email.params,
            'IMAGE_BASE_URL': config.client.subscriber_webapp_url,
            'plan_color': config.plan_color_hex,
            'plan_text_color': config.plan_text_color_hex,

        }

class FakeEmailClient(EmailClient):
    def send(self, email):
        recipient_email = email.recipient
        subject = email.params.get('subject', f'Mail from {email.client.name}')
        template = self.get_template(email.email_type)
        body = render_to_string(template, self.get_template_params(email))

        print('Email sent: %s', {
            'recipient': recipient_email,
            'subject': subject,
            'body': body,
            'attachments': email.attachments
        })
        logger.info('Email sent: %s', {
            'recipient': recipient_email,
            'subject': subject,
            'body': body
        })

class SMTPClient(EmailClient):
    def send(self, email):  # pylint: disable=too-many-locals
        config = email.client.email_config
        smtp_server = config.endpoint
        smtp_port = 587
        smtp_username = config.identity
        smtp_password = config.credential

        sender_email = config.from_address
        recipient_email = email.recipient
        template = self.get_template(email.email_type)

        subject = email.params.get('subject', f'Mail from {email.client.name}')
        body = render_to_string(template, self.get_template_params(email))

        message = MIMEMultipart('related')
        message['From'] = sender_email
        message['To'] = recipient_email
        message['Subject'] = subject

        message_body = MIMEMultipart('alternative')

        recipients = [recipient_email]
        if config.debug_bcc:
            recipients.append(config.debug_bcc)

        message_body.attach(MIMEText(body, 'html'))
        message.attach(message_body)
        for attachment in email.attachments:
            message.attach(attachment)

        with smtplib.SMTP(smtp_server, smtp_port) as server:
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.sendmail(sender_email, recipients, message.as_string())


def send_verification_email(recipient, client, subscriber_id):
    timestamp = int(time.time())
    verification_code = generate_code()

    subscriber = Subscriber.objects.get(pk=subscriber_id)
    subscriber.verification_code = verification_code
    subscriber.save()
    result = create_and_send_email(recipient, f'verify-code-{subscriber_id}-{timestamp}', 'verify-subscriber', client, {
        'subject': f'Verification code: {verification_code} ({client.name})',
        'verification_code': verification_code,
    })
    print(verification_code)
    return result


def send_forgot_password_email(recipient, client, subscriber_id):
    timestamp = int(time.time())
    verification_code = generate_code()

    subscriber = Subscriber.objects.get(pk=subscriber_id)
    subscriber.forgot_password_code = verification_code
    subscriber.save()
    result = create_and_send_email(recipient, f'forgot-password-{subscriber_id}-{timestamp}', 'forgot-password', client, {
        'subject': f'Forgot password code: {verification_code}',
        'verification_code': verification_code,
    })
    print(verification_code)
    return result


def send_port_in_requested_email(recipient, client, subscription_id, number, port_date=None):
    timestamp = int(time.time())
    date_string = port_date.strftime('%d/%m/%Y') if port_date else '3 working days'

    result = create_and_send_email(recipient, f'port-number-{subscription_id}-{timestamp}', 'port-number', client, {
        'subject': 'Number transfer confirmation',
        'port_date': date_string,
        'phone_number': number,
    })
    return result

def send_esim_activation_email(recipient, client, subscription_id):
    subscription = Subscription.objects.get(subscriber__client=client, pk=subscription_id)
    key = f'esim-activation-{subscription_id}'
    if _should_send_email(client, key):
        result = create_and_send_email(recipient, key, 'esim-activation', client, {
            # Add right subject
            'subject': f'{client.name} eSIM',
            'phone_number': subscription.latest_msisdn,
            'attachments': [
                {
                    'subtype': 'png',
                    'header_args': [
                        ('Content-ID', '<qr_code>'),
                        ('X-Attachment-Id', 'qr_code'),
                        {
                            '_name': 'Content-Disposition',
                            '_value': 'inline',
                            'filename': 'qr_code.png'
                        }
                    ],
                    'bytes_as_base64': subscription.latest_sim.esim_qr_as_base64
                },
                {
                    'subtype': 'png',
                    'header_args': [
                        {
                            '_name': 'Content-Disposition',
                            '_value': 'attachment',
                            'filename': 'esim_qr_code.png'
                        }
                    ],
                    'bytes_as_base64': subscription.latest_sim.esim_qr_as_base64
                }

            ]
        })
        return result
    return None

def send_self_sim_activation_email(recipient, client, subscription_id):
    subscription = Subscription.objects.get(subscriber__client=client, pk=subscription_id)
    key = f'self-activation-{subscription_id}'
    if _should_send_email(client, key):
        result = create_and_send_email(recipient, key, 'self-activation', client, {
            'subject': f'{client.name} SIM activation',
            'phone_number': subscription.latest_msisdn,
        })
        return result
    return None


def send_physical_delivery_email(subscription_id, client):
    subscription = Subscription.objects.get(subscriber__client=client, pk=subscription_id)
    key = f'physical_delivery-{subscription_id}'
    recipient = subscription.subscriber.email
    if _should_send_email(client, key):
        result = create_and_send_email(recipient, key, 'physical-delivery', client, {
            'subject': 'SIM order confirmation',
            'delivery_date': (timezone.now() + timedelta(days=2)).strftime('%d/%m/%Y'),
            'delivery_method': "Royal Mail First Class",
            'address': subscription.subscriber.address,
            'order_no': subscription.pk,
            'phone_number': subscription.latest_msisdn,
        })
        return result
    return None

def send_welcome_new_customer_email(subscription_id, client):
    subscription = Subscription.objects.get(subscriber__client=client, pk=subscription_id)
    key = f'welcome-new-customer-{subscription_id}'
    plan = subscription.latest_plan or subscription.intended_plan
    recipient = subscription.subscriber.email
    if _should_send_email(client, key):
        result = create_and_send_email(recipient, key, 'welcome-new-customer', client, {
            'subject': f'Welcome to {client.name}!',
            'plan_name': plan.name,
            'plan_price': str(plan.price),
            'current_plan_price': str(plan.current_price_for_new_subscribers),
            'phone_number': subscription.latest_number.phone_number if subscription.latest_number else None,

            'plan_data': plan.data_limit_display_qty_suffix,
            'plan_texts': plan.sms_limit_display_qty_suffix,
            'plan_calls': plan.voice_limit_display_qty_suffix,
    #        'plan_roaming': '10GB in the EU',
            'activation_url': client.subscriber_webapp_url,
            'perks': []
            #     {
            #         'name': "test",
            #         'image': "url",
            #         'progress': 10,
            #         'points_left': '270'
            #     }
            # ]
        })
        return result
    return None

def send_voucher_details(recipient, client, redemption, subscriber_id):
    timestamp = int(time.time())
    perk = redemption.perk
    result = create_and_send_email(recipient, f'voucher-details-{subscriber_id}-{perk.id}-{timestamp}', 'voucher-details', client, {
        'subject': 'A little something just for you...',
        'voucher_name': perk.merchant_name or 'Voucher',
        'voucher_code': perk.code,
        'voucher_claim_url': perk.url,
        'voucher_instructions': perk.instructions,
        'expiry_date': perk.expiry_date.strftime('%d/%m/%Y') if perk.expiry_date else None,
#        'voucher_image': perk.image,
        'vouchers_in_progress': []
        #     {
        #         'name': "test",
        #         'image': "url",
        #         'progress': 10,
        #         'points_left': '270'
        #     }
        # ]
    })
    return result

def send_password_changed(recipient, client):
    timestamp = int(time.time())
    result = create_and_send_email(recipient, f'password-changed-{timestamp}', 'password-changed', client, {
        'subject': 'Password change confirmation',
    })
    return result

def send_payment_failed(recipient, client):
    timestamp = int(time.time())
    result = create_and_send_email(recipient, f'payment-failed-{timestamp}', 'payment-failed', client, {
        'subject': 'Important: Payment failure',
    })
    return result

def send_no_payment_email(subscription_id, client):
    subscription = Subscription.objects.get(subscriber__client=client, pk=subscription_id)
    recipient = subscription.subscriber.email
    result = create_and_send_email(recipient, f'no-payment-{subscription_id}', 'no-payment', client, {
        'subject': 'Don’t miss your rewards!',
    })
    return result

def send_porting_complete_email(subscription_id, port_id, client, msisdn):
    subscription = Subscription.objects.get(subscriber__client=client, pk=subscription_id)
    recipient = subscription.subscriber.email
    result = create_and_send_email(recipient, f'porting-complete-{subscription.pk}-{port_id}', 'porting-complete', client, {
        'subject': 'Number port complete',
        'subscriber_name': subscription.subscriber.name,
        'msisdn': msisdn
    })
    return result

def send_porting_failed_email(subscription_id, port_id, client):
    subscription = Subscription.objects.get(subscriber__client=client, pk=subscription_id)
    recipient = subscription.subscriber.email
    result = create_and_send_email(recipient, f'porting-failed-{subscription.pk}-{port_id}', 'porting-failed', client, {
        'subject': 'Number transfer failure',
        'subscriber_name': subscription.subscriber.name,
    })
    return result

def send_cancel_subscription_email(subscription_id, client):
    subscription = Subscription.objects.get(subscriber__client=client, pk=subscription_id)
    recipient = subscription.subscriber.email
    timestamp = int(time.time())
    result = create_and_send_email(recipient, f'cancellation-{subscription_id}-{timestamp}', 'cancellation', client, {
        'subject': 'Cancellation confirmed',
        'termination_date': (STRIPE_BILLING_TIME_CONTROL.start_of_next_cycle).strftime('%d/%m/%Y')
    })
    return result

# call it somewhere
def send_cancellation_pac_email(recipient, client):
    timestamp = int(time.time())
    result = create_and_send_email(recipient, f'cancellation-pac-{timestamp}', 'cancellation-pac', client, {
        'subject': f'{client.name} PAC',
        'pac_code': "pac_code",
    })
    return result

# call it somewhere
def send_cancellation_stac_email(recipient, client):
    timestamp = int(time.time())
    result = create_and_send_email(recipient, f'cancellation-stac-{timestamp}', 'cancellation-stac', client, {
        'subject': f'{client.name} STAC',
        'stac_code': "stac_code",
    })
    return result

def _should_send_email(client, key):
    return not SystemEmail.objects.filter(client=client, recipient_key=key).exists()
