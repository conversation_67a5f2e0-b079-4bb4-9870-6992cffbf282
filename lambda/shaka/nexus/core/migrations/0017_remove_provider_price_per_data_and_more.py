# Generated by Django 4.2.7 on 2024-02-11 19:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0016_plan_data_bar_or_upgrade_threshold_override_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='provider',
            name='price_per_data',
        ),
        migrations.RemoveField(
            model_name='provider',
            name='price_per_sms',
        ),
        migrations.RemoveField(
            model_name='provider',
            name='price_per_voice',
        ),
        migrations.RemoveField(
            model_name='provider',
            name='price_unlimited_data',
        ),
        migrations.RemoveField(
            model_name='provider',
            name='price_unlimited_sms',
        ),
        migrations.RemoveField(
            model_name='provider',
            name='price_unlimited_voice',
        ),
        migrations.AddField(
            model_name='provider',
            name='unlimited_data_actual_limit',
            field=models.IntegerField(default=100, help_text='Actual data limit in gb'),
        ),
    ]
