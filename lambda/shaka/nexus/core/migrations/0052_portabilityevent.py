# Generated by Django 4.2.7 on 2024-04-04 13:28

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0051_alter_numberassignment_phone_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='PortabilityEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(max_length=20)),
                ('msisdn', models.CharField(max_length=30)),
                ('sim', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='portability_events', to='core.sim')),
            ],
            options={
                'unique_together': {('sim', 'msisdn')},
            },
        ),
    ]
