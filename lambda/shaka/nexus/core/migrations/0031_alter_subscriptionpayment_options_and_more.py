# Generated by Django 4.2.7 on 2024-03-03 19:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0030_paymentintegration'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='subscriptionpayment',
            options={'ordering': ['-date']},
        ),
        migrations.AddField(
            model_name='subscriber',
            name='billing_subscriber_id',
            field=models.CharField(blank=True, default='', max_length=100),
        ),
        migrations.AddField(
            model_name='subscription',
            name='billing_subscription_id',
            field=models.CharField(blank=True, default='', max_length=100),
        ),
        migrations.AddField(
            model_name='subscriptionpayment',
            name='billing_invoice_id',
            field=models.CharField(blank=True, db_index=True, default='', max_length=100),
        ),
        migrations.AddField(
            model_name='webhookactionlog',
            name='client',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.client'),
        ),
        migrations.AlterField(
            model_name='paymentintegration',
            name='client',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payment_integration', to='core.client'),
        ),
        migrations.AlterField(
            model_name='subscriber',
            name='client',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscribers', to='core.client'),
        ),
        migrations.AlterField(
            model_name='subscriptionpayment',
            name='amount',
            field=models.DecimalField(decimal_places=2, max_digits=10),
        ),
        migrations.AlterField(
            model_name='subscriptionpayment',
            name='currency',
            field=models.CharField(choices=[('GBX', 'GBX'), ('GBP', 'GBP'), ('USD', 'USD'), ('EUR', 'EUR')], default='GBX', max_length=3),
        ),
    ]
