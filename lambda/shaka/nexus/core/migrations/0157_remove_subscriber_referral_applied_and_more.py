# Generated by Django 4.2.7 on 2025-02-05 10:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0156_subscriber_referral_applied'),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name='subscriber',
            name='referral_applied',
        ),
        migrations.AddField(
            model_name='subscriber',
            name='referral_code',
            field=models.CharField(blank=True, default='', max_length=20),
        ),
        migrations.AddField(
            model_name='subscriber',
            name='referred_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='referrals', to='core.subscriber'),
        ),
    ]
