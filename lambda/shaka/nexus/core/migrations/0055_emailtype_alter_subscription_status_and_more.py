# Generated by Django 4.2.7 on 2024-04-28 15:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0054_gbpgbx'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
            ],
        ),
        migrations.AlterField(
            model_name='subscription',
            name='status',
            field=models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('cancelled', 'Cancelled')], default='inactive', max_length=20),
        ),
        migrations.CreateModel(
            name='EmailConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider', models.CharField(choices=[('ses', 'Amazon SES'), ('smtp', 'SMTP')], default='smtp', max_length=4)),
                ('identity', models.CharField(blank=True, max_length=255, null=True)),
                ('credential', models.CharField(blank=True, max_length=255, null=True)),
                ('endpoint', models.CharField(blank=True, max_length=255, null=True)),
                ('from_address', models.EmailField(max_length=254)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
                ('email_types', models.ManyToManyField(to='core.emailtype')),
            ],
        ),
    ]
