# Generated by Django 4.2.7 on 2024-09-24 14:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0115_add_esim_and_phys_emails'),
    ]

    operations = [
        migrations.AddField(
            model_name='emailconfiguration',
            name='plan_color_hex',
            field=models.CharField(default='#FFFFFF', help_text='Hex color for the plan, e.g. #123456', max_length=7),
        ),
        migrations.AddField(
            model_name='subscription',
            name='esim_recipient',
            field=models.EmailField(blank=True, default='', max_length=254),
        ),
    ]
