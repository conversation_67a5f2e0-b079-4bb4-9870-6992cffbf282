# Generated by Django 4.2.7 on 2024-06-24 12:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0082_merge_20240624_1005'),
    ]

    operations = [
        migrations.AddField(
            model_name='planchange',
            name='target_plan_change',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cancellation', to='core.planchange'),
        ),
        migrations.AlterField(
            model_name='planchange',
            name='status',
            field=models.CharField(choices=[('in_progress', 'In Progress'), ('locked', 'Locked'), ('complete', 'Complete'), ('cancelled', 'Cancelled'), ('errored', 'Errored')], default='in_progress', max_length=15),
        ),
    ]
