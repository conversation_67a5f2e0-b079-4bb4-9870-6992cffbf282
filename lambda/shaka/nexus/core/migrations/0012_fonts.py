# Generated by Django 4.2.7 on 2024-01-02 14:02

from django.db import migrations

def create_fonts(apps, schema_editor):
    Font = apps.get_model('core', 'Font')

    # Create two fonts
    Font.objects.create(name='Baloo 2')
    Font.objects.create(name='Inter')


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0011_font_clientbranding'),
    ]

    operations = [
        migrations.RunPython(create_fonts)
    ]
