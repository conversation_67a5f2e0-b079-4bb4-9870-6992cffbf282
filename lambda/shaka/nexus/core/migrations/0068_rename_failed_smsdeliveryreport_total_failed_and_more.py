# Generated by Django 4.2.7 on 2024-06-05 00:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0067_smsconfiguration_delivery_report_url_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='smsdeliveryreport',
            old_name='failed',
            new_name='total_failed',
        ),
        migrations.RenameField(
            model_name='smsdeliveryreport',
            old_name='pending',
            new_name='total_pending',
        ),
        migrations.RemoveField(
            model_name='smsdeliveryreport',
            name='sender',
        ),
        migrations.AlterField(
            model_name='webhooklog',
            name='webhook_source',
            field=models.CharField(choices=[('transatel', 'Transatel'), ('stripe', 'Stripe'), ('fake_transatel', 'Fake Transatel'), ('fake_stripe', 'Fake Stripe'), ('infobip', 'Infobip')], max_length=20),
        ),
    ]
