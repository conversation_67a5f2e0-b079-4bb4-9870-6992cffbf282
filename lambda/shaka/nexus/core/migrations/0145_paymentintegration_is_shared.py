# Generated by Django 4.2.7 on 2024-12-03 02:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0144_boltonpurchase_expiry'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentintegration',
            name='is_shared',
            field=models.BooleanField(default=False, help_text='Whether this payment integration is shared with other clients. Tick it on all shared payment integrations. This will soften error checking when entities cannot be found and prevent duplication.'),
        ),
    ]
