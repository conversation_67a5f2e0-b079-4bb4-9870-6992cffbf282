# Generated by Django 4.2.7 on 2024-08-08 01:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0096_alter_discountperk_options_alter_perk_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PerkRedemption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('redeemed_on', models.DateTimeField(auto_now_add=True)),
                ('points_paid', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('fulfilment_status', models.CharField(choices=[('pending', 'Pending'), ('fulfilled', 'Fulfilled'), ('errored', 'Errored')], default='pending', max_length=20)),
                ('perk', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='redemptions', to='core.perk')),
                ('subscriber', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='perk_redemptions', to='core.subscriber')),
            ],
        ),
    ]
