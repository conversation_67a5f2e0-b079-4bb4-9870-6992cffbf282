# Generated by Django 4.2.7 on 2024-08-08 01:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('core', '0095_perk_discountperk_plandiscount_discount_perk'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='discountperk',
            options={'base_manager_name': 'objects'},
        ),
        migrations.AlterModelOptions(
            name='perk',
            options={'base_manager_name': 'objects'},
        ),
        migrations.AddField(
            model_name='perk',
            name='polymorphic_ctype',
            field=models.ForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='polymorphic_%(app_label)s.%(class)s_set+', to='contenttypes.contenttype'),
        ),
    ]
