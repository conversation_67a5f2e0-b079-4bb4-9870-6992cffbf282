# Generated by Django 4.2.7 on 2024-09-16 10:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0109_remove_sim_type_sim_esim_data_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='sim',
            name='esim_available_to',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='esims', to='core.provider'),
        ),
    ]
