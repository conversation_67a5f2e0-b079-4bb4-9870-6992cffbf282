# Generated by Django 4.2.7 on 2023-12-14 16:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_client_auth_app_client_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Plan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='Sim',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.CharField(max_length=20)),
                ('status', models.CharField(choices=[('inactive', 'Inactive'), ('active', 'Active'), ('suspended', 'Suspended'), ('terminated', 'Terminated')], default='inactive', max_length=20)),
            ],
        ),
        migrations.CreateModel(
            name='SimSubscriptionAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('sim', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.sim')),
            ],
        ),
        migrations.CreateModel(
            name='Subscriber',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254)),
                ('join_date', models.DateTimeField()),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
            ],
        ),
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField()),
                ('sims', models.ManyToManyField(through='core.SimSubscriptionAssignment', to='core.sim')),
                ('subscriber', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.subscriber')),
            ],
        ),
        migrations.AddField(
            model_name='simsubscriptionassignment',
            name='subscription',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.subscription'),
        ),
        migrations.CreateModel(
            name='SimPlanAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.plan')),
                ('sim', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.sim')),
            ],
        ),
        migrations.AddField(
            model_name='sim',
            name='plans',
            field=models.ManyToManyField(through='core.SimPlanAssignment', to='core.plan'),
        ),
        migrations.CreateModel(
            name='NumberAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', models.CharField(max_length=15)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('sim', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.sim')),
            ],
        ),
    ]
