# Generated by Django 4.2.7 on 2024-06-19 15:22

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0077_merge_20240610_1914'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlanChange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('change_type', models.CharField(choices=[('upgrade', 'Upgrade'), ('downgrade', 'Downgrade'), ('cancel_change', 'Cancel Change'), ('cancellation', 'Cancellation')], max_length=20)),
                ('execution_start_time', models.DateTimeField()),
                ('status', models.CharField(choices=[('in_progress', 'In Progress'), ('actuating', 'Actuating'), ('done', 'Done'), ('errored', 'Errored')], default='in_progress', max_length=15)),
                ('execution_id', models.CharField(max_length=255)),
                ('sub_execution_id', models.CharField(blank=True, max_length=255, null=True)),
                ('task_token', models.CharField(blank=True, max_length=255, null=True)),
                ('subscription', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='core.subscription')),
                ('target_plan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.plan')),
            ],
            options={
                'verbose_name': 'Plan Change',
                'verbose_name_plural': 'Plan Changes',
            },
        ),
    ]
