# Generated by Django 4.2.7 on 2024-06-23 16:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0077_merge_20240610_1914'),
    ]

    operations = [
        migrations.CreateModel(
            name='IndividualSMSDelivery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('delivered', 'Delivered'), ('pending', 'Pending'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('recipient', models.CharField(max_length=30)),
                ('units_sent', models.IntegerField(default=0)),
                ('sms_message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='individual_deliveries', to='core.smsmessage')),
            ],
        ),
        migrations.DeleteModel(
            name='SMSDeliveryReport',
        ),
        migrations.AddIndex(
            model_name='individualsmsdelivery',
            index=models.Index(fields=['recipient'], name='core_indivi_recipie_29a8fc_idx'),
        ),
        migrations.AddIndex(
            model_name='individualsmsdelivery',
            index=models.Index(fields=['sms_message', 'recipient', 'status'], name='core_indivi_sms_mes_8fd201_idx'),
        ),
        migrations.AddIndex(
            model_name='individualsmsdelivery',
            index=models.Index(fields=['sms_message', 'recipient'], name='core_indivi_sms_mes_33ec14_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='individualsmsdelivery',
            unique_together={('sms_message', 'recipient')},
        ),
    ]
