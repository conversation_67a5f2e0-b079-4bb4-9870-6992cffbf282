from datetime import datetime
from django.conf import settings
import pytz

def gb_to_bytes(gb):
    return int(gb * 1024 * 1024 * 1024)

def bytes_to_gb(n_bytes):
    return n_bytes / 1024.0 / 1024.0 / 1024.0

def epoch_to_utc(epoch_timestamp):
    utc_datetime = datetime.utcfromtimestamp(epoch_timestamp)
    utc_aware_datetime = utc_datetime.replace(tzinfo=pytz.utc)
    return utc_aware_datetime

def phone_number_to_msisdn(phone_number):
    stripped = phone_number.replace(' ', '')
    if stripped[0] == '0':
        return f'44{stripped[1:]}'
    else:
        return stripped.replace('+44', '44')

def ensure_uk_prefix(phone_number):
    return f'+{phone_number_to_msisdn(phone_number)}'

def get_stripe_client(*args, **kwargs):
    """Lazy import stripe"""
    from stripe import StripeClient  # pylint:disable=import-outside-toplevel
    if settings.STRIPE_BETA:
        return StripeClient(*args, stripe_version='2025-03-31.basil;custom_checkout_beta=v1', **kwargs)
    return StripeClient(*args, **kwargs)

def format_datetime_for_step_functions(dt):
    utc_tz = pytz.timezone('UTC')
    dt = dt.astimezone(utc_tz)
    return dt.strftime('%Y-%m-%dT%H:%M:%SZ')

def calculate_luhn(checkless_number):
    def digits_of(n):
        return [int(d) for d in str(n)]

    digits = digits_of(checkless_number)
    odd_digits = digits[-2::-2]
    even_digits = digits[-1::-2]
    checksum = sum(odd_digits) + sum((d * 2 if d * 2 < 10 else d * 2 - 9 for d in even_digits))
    return (10 - (checksum % 10)) % 10
