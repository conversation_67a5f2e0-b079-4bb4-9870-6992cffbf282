import time
import logging
from collections import defaultdict
import requests
from django.core.management.base import BaseCommand
from django.conf import settings
from core.billing import get_msisdns_over_data_threshold, get_usage_for_msisdn_between_dates
from core.models import NumberAssignment, Sim, Plan
from core.utils import bytes_to_gb
from core.time_control import TRANSATEL_TIME_CONTROL, GAMMA_TIME_CONTROL
from core.provider_interface import GammaInterface


logger = logging.getLogger()
logger.setLevel(logging.INFO)


known_test_msisdns = [
    '447421123539', '447421123537', '447579761279', '447383074723', '447721055065', '447529796026', # transatel fellas
    '447421122840', # chocolate box transatel
    '447356300165', # simp
    '447356191744', # ancient test sim
    '447356300186', '447421122826', # millwall sim
    '447356300187', '447356300188', # Perkfon test
    '447356300172', # simp test
]


msisdn_send_times = {}

class Command(BaseCommand):
    help = 'Attempt to prevent subscribers from exceeding their usage cap'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.warnings_by_msisdn = defaultdict(list)
        self.warnings_by_plan = defaultdict(list)

    def add_arguments(self, parser):
        parser.add_argument('--dry-run', action='store_true', help='Do not actually bar or upgrade, just print what would be done')

    def get_plans_to_check(self):
        return Plan.objects.filter(client__provider__is_demo=False)

    def lookup_prorated_usage(self, sim, msisdn, month_end):
        if sim.latest_plan_assignment and sim.latest_plan_assignment.start_date and sim.activation_date:
            base_date = max(sim.activation_date, sim.latest_plan_assignment.start_date)
        elif sim.activation_date:
            self._send_warning_message(f'Sim {sim.pk} has no plan dates')
            base_date = sim.activation_date
        else:
            self._send_warning_message(f'Sim {sim.pk} has no activation date')
            raise RuntimeError('Sim has no activation date')
        return get_usage_for_msisdn_between_dates(msisdn, base_date, month_end, 'data')

    def is_part_of_sim_swap(self, sim):
        return sim.is_old_sim_in_sim_swap

    def handle(self, *args, **options):
        self.pw_summary = ''
        checked_count = 0
        ok_count = 0
        self.dry_run = options['dry_run']
        for time_control in [TRANSATEL_TIME_CONTROL, GAMMA_TIME_CONTROL]:
            start, end = time_control.current_month_datetimes
            for plan in self.get_plans_to_check():
                if plan.client.provider.time_control != time_control:
                    continue
                plan_warning_threshold = plan.get_data_usage_warning_threshold_bytes()
                plan_bar_or_upgrade_threshold = plan.get_data_bar_or_upgrade_threshold_bytes()
                if plan_bar_or_upgrade_threshold <= plan_warning_threshold:
                    self.warn_by_plan(plan, (f'Plan {plan} has a bar threshold ({plan_bar_or_upgrade_threshold}) lower than its warning threshold ({plan_warning_threshold}), will not bar or upgrade!'))
                if plan_bar_or_upgrade_threshold < 512 * 1024 * 1024:
                    self.warn_by_plan(plan, (f'Plan {plan} has a strange bar or upgrade threshold, not doing anything'))
                    continue
                if plan_warning_threshold < 512 * 1024 * 1024:
                    self.warn_by_plan(plan, (f'Plan {plan} has a strange warning threshold, not doing anything'))
                    continue
                warning_results = get_msisdns_over_data_threshold(start, end, 11)
                usage_by_msisdn = {v['msisdn']: v['total_usage'] for v in warning_results}

                assignments_by_msisdn = self.get_relevant_assignments(usage_by_msisdn, plan, start, end)
                usage_by_sim = defaultdict(lambda: 0)
                for msisdn, assignments in assignments_by_msisdn.items():
                    for assignment in assignments:
                        sim = assignment.sim
                        if sim.started_new_plan_this_month:
                            usage = self.lookup_prorated_usage(sim, msisdn, end)
                        else:
                            usage = usage_by_msisdn[msisdn]
                        usage_by_sim[sim] += usage
                for sim, usage in usage_by_sim.items():
                    checked_count += 1
                    msisdn = sim.latest_msisdn
                    if sim.latest_plan != plan:
                        self.warn_by_plan(plan, f'Plan {plan} does not match sim plan, possibly an unsupported upgrade/downgrade, needs manual tech investigation.')
                        self.warn_by_msisdn(msisdn, f'{msisdn} is at {usage} usage but sim plan does not match')
                        self.pw_summary += f'\n{msisdn} - Plan mismatch'
                        continue
                    warning_threshold = sim.warning_threshold_bytes_this_month
                    bar_or_upgrade_threshold = sim.bar_or_upgrade_threshold_bytes_this_month
                    if self.is_part_of_sim_swap(sim):
                        self.pw_summary += f'\n{msisdn} - Sim swap'
                        continue
                    if usage > bar_or_upgrade_threshold:
                        if sim.provider_plan_override:
                            if sim.provider_plan_override == 'TSL_UK_DATA_30GB':
                                self.upgrade_background_plan(msisdn, sim.serial_number)
                            else:
                                self.pw_summary += f'\n{msisdn} - background plan issue'
                                raise RuntimeError('No idea what background plan this is')
                        upgrade_target = plan.upgrade_target
                        if upgrade_target:
                            if upgrade_target.data_limit_bytes <= plan.data_limit_bytes:
                                self.warn_by_plan(plan, f'Plan {plan} has an upgrade target with a lower data limit than itself, too scared to upgrade, needs manual fixing.')
                                self.warn_by_msisdn(msisdn, f'{msisdn} is at or above its threshold but there is no valid plan upgrade target so not doing anything')
                                self.pw_summary += f'\n{msisdn} - at or above'
                            elif upgrade_target.data_limit_gb != 100:
                                self.warn_by_plan(plan, f'Plan {plan} has an upgrade target that is not 100gb, too scared to upgrade, needs manual fixing')
                                self.warn_by_msisdn(msisdn, f'{msisdn} is at or above its threshold but there is no valid plan upgrade target so not doing anything')
                                self.pw_summary += f'\n{msisdn} - at or above'
                            else:
                                self.upgrade_plan(msisdn, sim.serial_number, current_plan=plan, target_plan=upgrade_target)
                                self.pw_summary += f'\n{msisdn} - arb up'
                        else:
                            if sim.is_data_barred:
                                self.pw_summary += f'\n{msisdn} - is barred'
                                pass
                            else:
                                self.pw_summary += f'\n{msisdn} - barring'
                                self.bar_data(msisdn, sim, plan, sim.client.provider.name)
                    elif usage > warning_threshold:
                        usage_gb = bytes_to_gb(usage)
                        limit_gb = plan.data_limit_gb
                        usage_percent = plan.data_usage_bytes_percent(usage)
                        self.pw_summary += f'\n{msisdn} - @ percent {usage_percent}'
                        self.warn_by_msisdn(msisdn, f'{msisdn} is approaching its upgrade threshold on {plan}, currently {usage_gb:0.2f}gb / {limit_gb:0.2f}gb, {usage_percent:0.2f}%, might need to upgrade manually soon (and update nexus)', timeout=3600, key='approaching-threshold')
                    else:
                        self.pw_summary += f'\n{msisdn} - ok'
                        ok_count += 1
        if not self.dry_run:
            self.warn_by_msisdn('all', 'bar data check ran', timeout=3600, key='bar-data-check-ran')
        self.send_warnings()
        if not self.dry_run:
            requests.post("https://processwarden.com/tubes/168/bar-data-check-ran/post_item/", json={'status': 'completed', 'checked': checked_count, 'ok': ok_count, 'summary': self.pw_summary})

    def is_in_timeout(self, msisdn, key, timeout):
        last_send_time = msisdn_send_times.get((msisdn, key), 0)
        return last_send_time + timeout > time.time()

    def warn_by_plan(self, plan, warning):
        self.warnings_by_plan[plan].append(warning)

    def warn_by_msisdn(self, msisdn, warning, timeout=None, key=None):
        should_send = (not timeout) or (not self.is_in_timeout(msisdn, key, timeout))
        if should_send:
            self.warnings_by_msisdn[msisdn].append(warning)
            if timeout:
                msisdn_send_times[(msisdn, key)] = time.time()

    def send_warnings(self):
        plan_warnings = ''
        for plan, warnings in self.warnings_by_plan.items():
            plan_warnings += f'Warnings for {plan}:\n\t' + ('\n\t'.join(warnings)) + '\n'

        msisdn_warnings = ''
        for msisdn, warnings in self.warnings_by_msisdn.items():
            msisdn_warnings += f'Warnings for {msisdn}:\n\t' + ('\n\t'.join(warnings)) + '\n'
        warning_msg = f'{plan_warnings}\n{msisdn_warnings}'
        if self.warnings_by_msisdn or self.warnings_by_plan:
            self._send_warning_message(warning_msg)

    def _send_warning_message(self, msg):
        print(msg)
        if self.dry_run:
            return
        slack_webhook = '*********************************************************************************'
        logger.warn(msg)
        requests.post(slack_webhook, headers={'Content-type': 'application/json'}, json={"text":msg})

    def upgrade_plan(self, msisdn, serial_number, current_plan, target_plan):
        self.trigger_provider_action('real-plan-upgrade-transatel', {'sim_serial': serial_number, 'from_plan': current_plan.pk, 'to_plan': target_plan.pk, 'transatel_package_id': 'TSL_UK_DATA_100GB'})
        self.warn_by_msisdn(msisdn, f'Need plan upgrade for {msisdn} on {current_plan}, will be automated eventually but for now is manual. Idea is to upgrade to {target_plan}. Make sure to create a new plan assignment in the nexus and update the end date of the old plan assignment for the sim')

    def upgrade_background_plan(self, msisdn, serial_number):
        self.trigger_provider_action('real-plan-upgrade-transatel', {'sim_serial': serial_number, 'transatel_package_id': 'TSL_UK_DATA_100GB'})
        sim = Sim.objects.get(serial_number=serial_number)
        sim.provider_plan_override = None
        sim.save()
        self.warn_by_msisdn(msisdn, f'Did background upgrade for {msisdn}')

    def bar_data(self, msisdn, sim, plan, provider_name):
        serial_number = sim.serial_number
        if provider_name.lower() == 'transatel':
            self.trigger_provider_action('real-bar-data-transatel', {'sim_serial': serial_number, 'from_plan': plan.pk})
        elif provider_name.lower() == 'gamma':
            GammaInterface().bar_data(sim)
        else:
            self.warn_by_msisdn(msisdn, f'Cannot bar {msisdn} on {plan} because unknown provider')
        self.warn_by_msisdn(msisdn, f'Barring {msisdn} on {plan}')

    def trigger_provider_action(self, action, payload):
        if self.dry_run:
            logger.info(f'Dry run: Would have triggered provider action {action} with payload {payload}')
            print(f'Dry run: Would have triggered provider action {action} with payload {payload}')
            return
        api_url = f"{settings.CDR_DB_API_ENDPOINT}/provider/provider-actions/"

        response = requests.post(api_url, headers={'Authorization': f'Bearer {settings.CDR_DB_API_TOKEN}'}, json={
            'action': action,
            'data_payload': payload,
            'status': 'not_started'
        }, timeout=20)

        response.raise_for_status()

    def get_relevant_assignments(self, usage_by_msisdn, plan, start_date, end_date):
        msisdns = usage_by_msisdn.keys()
        known_assignments = NumberAssignment.objects.filter(phone_number__in=msisdns).date_filter(start_date, end_date)
        known_msisdns = set([na.phone_number for na in known_assignments])
        for msisdn in msisdns:
            if msisdn not in known_msisdns and msisdn not in known_test_msisdns:
                self.warn_by_msisdn(msisdn, f'Unknown msisdn, cannot check usage but might be close to threshold: {msisdn}, usage: {usage_by_msisdn[msisdn]} ({bytes_to_gb(usage_by_msisdn[msisdn]):0.2f})', timeout=1200, key='unknown-msisdn')
        relevant_assignments = {}
        for known_assignment in known_assignments:
            if known_assignment.sim.latest_plan == plan:
                if known_assignment.phone_number not in relevant_assignments:
                    relevant_assignments[known_assignment.phone_number] = [known_assignment]
                else:
                    relevant_assignments[known_assignment.phone_number].append(known_assignment)
        return relevant_assignments
