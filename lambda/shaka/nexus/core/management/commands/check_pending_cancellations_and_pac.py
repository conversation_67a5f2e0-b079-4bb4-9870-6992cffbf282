from django.core.management.base import BaseCommand, CommandError
from core.models import Client, Subscription, PlanChange, Sim
from core.utils import get_stripe_client

class Command(BaseCommand):
    help = 'Find common/legacy issues with subscriptions and report on them'

    def add_arguments(self, parser):
        parser.add_argument('--client-id', type=int, help='Client ID to check')

    def handle(self, *args, **options):
        self.client = Client.objects.get(pk=options['client_id'])
        self.stripe_client = get_stripe_client(self.client.payment_integration.secret_credentials)
        subscriptions = Subscription.objects.filter(subscriber__client=self.client)
        self.look_for_cancellation_plan_changes(subscriptions)
        self.look_for_terminated_or_suspended_sims(subscriptions)
        self.check_for_unbars(subscriptions)
        self.check_for_non_payments_or_cancellations_in_stripe(subscriptions)
        self.check_for_late_payments(subscriptions)
        self.check_for_terminated_sims_with_active_stripe_subscriptions(subscriptions)

    def look_for_cancellation_plan_changes(self, subscriptions):
        cancelled_subscriptions = []
        for subscription in subscriptions:
            if c := subscription.plan_changes.filter(status=PlanChange.Status.IN_PROGRESS, change_type=PlanChange.ChangeType.CANCELLATION).first():
                stripe_subscription = self.stripe_client.subscriptions.retrieve(subscription.billing_subscription_id)
                print(stripe_subscription['status'])
                if stripe_subscription['status'] != 'canceled':
                     cancelled_subscriptions.append((subscription, c, stripe_subscription))

        if cancelled_subscriptions:
            print(f'{len(cancelled_subscriptions)} subscriptions have a cancellation in progress:')
            for subscription, plan_change, stripe_sub in cancelled_subscriptions:
                print(f'{subscription} cancelled on - {plan_change.execution_start_time}, [{stripe_sub["status"]}], iccid: {subscription.latest_sim.serial_number if subscription.latest_sim else "None"}')

    def look_for_terminated_or_suspended_sims(self, subscriptions):
        self.client.provider.look_for_pac_out([sub.latest_sim.serial_number for sub in subscriptions if sub.latest_sim and sub.status == Subscription.Statuses.ACTIVE])

    def check_for_unbars(self, subscriptions):
        for subscription in subscriptions:
            if subscription.latest_sim and subscription.latest_sim.is_data_barred:
                print(f'{subscription} has a barred sim, will unbar - iccid: {subscription.latest_sim.serial_number}')

    def check_for_non_payments_or_cancellations_in_stripe(self, subscriptions):
        for subscription in subscriptions:
            stripe_subscription = self.stripe_client.subscriptions.retrieve(subscription.billing_subscription_id)
            if stripe_subscription['status'] != 'active' and subscription.status == Subscription.Statuses.ACTIVE:
                print(f'{subscription} has a status of {stripe_subscription["status"]}, iccid: {subscription.latest_sim.serial_number if subscription.latest_sim else "None"}, will need cancelling')
            if self.is_missing_a_subscription_payment(subscription) and stripe_subscription['status'] == 'active' and subscription.latest_sim:
                print(f'{subscription} is missing a payment, iccid: {subscription.latest_sim.serial_number if subscription.latest_sim else "None"}')

    def is_missing_a_subscription_payment(self, subscription):
        billing_dates = self.client.time_control.current_month_datetimes
        if subscription.start_date < billing_dates[1]:
            payments = subscription.payments.filter(date__gte=billing_dates[0], date__lt=billing_dates[1])
            return not payments.exists()
        return False

    def check_for_late_payments(self, subscriptions):
        for subscription in subscriptions:
            if not self.is_missing_a_subscription_payment(subscription):
                stripe_subscription = self.stripe_client.subscriptions.retrieve(subscription.billing_subscription_id)
                if stripe_subscription['status'] != 'active':
                    print(f'{subscription} needs reactivating (late payment), has a status of {stripe_subscription["status"]}, iccid: {subscription.latest_sim.serial_number if subscription.latest_sim else "None"}')

    def check_for_terminated_sims_with_active_stripe_subscriptions(self, subscriptions):
        for subscription in subscriptions:
            if subscription.latest_sim and subscription.latest_sim.status == Sim.SimStatuses.TERMINATED:
                stripe_subscription = self.stripe_client.subscriptions.retrieve(subscription.billing_subscription_id)
                if stripe_subscription['status'] == 'active':
                    print(f'{subscription} has a terminated sim with an active stripe subscription, iccid: {subscription.latest_sim.serial_number}')
