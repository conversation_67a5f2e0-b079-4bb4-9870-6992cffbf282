from django.core.management.base import BaseCommand, CommandError
from django.utils.dateparse import parse_date
import decimal
from core.models import Client
from core.utils import get_stripe_client
import pytz
from datetime import datetime

class Command(BaseCommand):
    help = 'Create a manual free trial for a client'

    def add_arguments(self, parser):
        parser.add_argument('--client-id', type=int, help='The ID of the client')
        parser.add_argument('--subscription-billing-id', type=str, help='The stripe customer billing id')
        parser.add_argument('--date', type=str, help='The date of the free trial end (YYYY-MM-DD)')

    def handle(self, *args, **options):
        client_id = options['client_id']
        subscription_billing_id = options['subscription_billing_id']
        date = options['date']
        date_in_europe_london = pytz.timezone('Europe/London').localize(datetime.strptime(date, '%Y-%m-%d')).replace(hour=0, minute=0, second=0, microsecond=0)
        print(date_in_europe_london.timestamp())
        extend_free_trial(client_id, subscription_billing_id, date_in_europe_london.timestamp())
            

def extend_free_trial(client_id, subscription_billing_id, date_timestamp):
    try:
        client = Client.objects.get(id=client_id)
    except Client.DoesNotExist:
        raise CommandError(f'Client with ID {client_id} does not exist')

    stripe_client = get_stripe_client(client.payment_integration.secret_credentials)
    stripe_client.subscriptions.update(subscription_billing_id, params={'trial_end': date_timestamp, 'proration_behavior': 'none'})
