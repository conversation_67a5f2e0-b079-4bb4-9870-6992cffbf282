# Management command to take an iccid, subscription id, or msisdn and schedule an unbar for it at 1 am just after the billing month

from django.core.management.base import BaseCommand, CommandError
from core.models import Subscription
from .utils import is_msisdn, is_subscription_id, is_iccid, lookup_iccid, lookup_client_by_iccid

class Command(BaseCommand):
    help = 'Schedule an unbar for a subscription'
        
    def add_arguments(self, parser):
        parser.add_argument('--identifier', type=str, help='ICCID, subscription ID, or MSISDN', required=True)
        
    def handle(self, *args, **options):
        identifier = options['identifier']
        iccid = None
        if is_msisdn(identifier):
            iccid = lookup_iccid(msisdn=identifier)
        elif is_subscription_id(identifier):
            iccid = lookup_iccid(subscription_id=identifier)
        elif is_iccid(identifier):
            iccid = identifier
        if not iccid:
            raise CommandError('Identifier must be an ICCID, subscription ID, or MSISDN')
        if iccid:
            client = lookup_client_by_iccid(iccid)
            client.provider.schedule_unbar(iccid)
