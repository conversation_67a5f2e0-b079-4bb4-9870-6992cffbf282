resource "aws_iam_policy" "prod_lambda_deploy_pipeline_policy" {
  name        = "prod-lambda-deploy-pipeline-policy"
  description = "Policy to allow codepipeline to deploy lambdas"
  policy      = <<EOF
{
    "Statement": [
        {
            "Action": [
                "iam:PassRole"
            ],
            "Resource": "*",
            "Effect": "Allow",
            "Condition": {
                "StringEqualsIfExists": {
                    "iam:PassedToService": [
                        "cloudformation.amazonaws.com",
                        "elasticbeanstalk.amazonaws.com",
                        "ec2.amazonaws.com",
                        "ecs-tasks.amazonaws.com"
                    ]
                }
            }
        },
        {
            "Action": [
              "logs:CreateLogGroup",
              "logs:CreateLogStream",
              "logs:PutLogEvents"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codedeploy:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "ec2:*",
                "autoscaling:*",
                "cloudwatch:*",
                "cloudformation:*",
                "lambda:*",
                "s3:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "cloudformation:CreateStack",
                "cloudformation:DeleteStack",
                "cloudformation:DescribeStacks",
                "cloudformation:UpdateStack",
                "cloudformation:CreateChangeSet",
                "cloudformation:DeleteChangeSet",
                "cloudformation:DescribeChangeSet",
                "cloudformation:ExecuteChangeSet",
                "cloudformation:SetStackPolicy",
                "cloudformation:ValidateTemplate"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codebuild:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codepipeline:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Effect": "Allow",
            "Action": [
              "codestar-notifications:*",
              "codestar-connections:*"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
              "sqs:*"
            ],
            "Resource": "${aws_sqs_queue.prod_pipeline_notification_queue.arn}"
        },
        {
            "Effect": "Allow",
            "Action": [
                "appconfig:StartDeployment",
                "appconfig:StopDeployment",
                "appconfig:GetDeployment"
            ],
            "Resource": "*"
        }
    ],
    "Version": "2012-10-17"
}
EOF
}

resource "aws_iam_role" "prod_lambda_deploy_pipeline_role" {
  name               = "prod-lambda-deploy-pipeline-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "codepipeline.amazonaws.com"
      },
      "Effect": "Allow"
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_codepipeline" "prod_lambda_deploy_pipeline" {
  for_each = var.lambdas
  name     = "prod-lambda-deploy-pipeline-${each.key}"
  role_arn = aws_iam_role.prod_lambda_deploy_pipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "S3"
      version          = "1"
      output_artifacts = ["source_output"]
      namespace        = "Source"

      configuration = {
        S3Bucket             = aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket
        S3ObjectKey          = "lambda-builds/prod/${each.key}_zipped.zip"
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Deploy"
    action {
      name            = "Deploy"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      version         = "1"
      input_artifacts = ["source_output"]

      configuration = {
        ProjectName = aws_codebuild_project.prod_meta_lambda_deploy_codebuild[each.key].name
      }
    }
  }
}

// https://repost.aws/questions/QUiKyt_-5-SdW2Gia9ICH7dA/canary-deployments-for-lambda-using-codedeploy-with-codepipeline

resource "aws_codebuild_project" "prod_meta_lambda_deploy_codebuild" {
  for_each     = var.lambdas
  name         = "prod-meta-lambda-deploy-codebuild-${each.key}"
  service_role = aws_iam_role.prod_lambda_deploy_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

phases:
  build:
    commands:
      - "echo '${data.local_file.lambda_appspec.content_base64}' | base64 --decode > appspec.yaml"
      - "ls $CODEBUILD_SRC_DIR"
      - "aws lambda update-function-code --region=eu-west-2 --function-name ${each.value.lambda_function_name} --zip-file fileb://$CODEBUILD_SRC_DIR/${each.key}.zip"
      - "aws lambda wait function-active --function-name ${each.value.lambda_function_name}"
      - "sleep 5"
      - "newVersion=$(aws lambda publish-version --function-name ${each.value.lambda_function_name} --query Version --output text)"
      - "currentVersion=$(aws lambda get-alias --function-name ${each.value.lambda_function_name} --name deployed --query FunctionVersion --output text)"
      - "sed -i \"s/%currentVersion%/$${currentVersion}/g\" appspec.yaml"
      - "sed -i \"s/%targetVersion%/$${newVersion}/g\" appspec.yaml"
      - "sed -i \"s/%name%/${each.value.lambda_function_name}/g\" appspec.yaml"
      - "aws s3 cp appspec.yaml s3://${aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket}/lambda-appspecs/prod_meta/${each.key}.yaml"
      - "D_OUTPUT=`aws deploy create-deployment --application-name ${aws_codedeploy_app.prod_lambda_meta_app.name} --deployment-group-name ${aws_codedeploy_deployment_group.prod_lambda_meta_group.deployment_group_name} --s3-location bucket=${aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket},bundleType=yaml,key=lambda-appspecs/prod_meta/${each.key}.yaml`"
      - "echo $D_OUTPUT"
      - "DEP_ID=`echo $D_OUTPUT | python -c 'import sys;import json;print(json.load(sys.stdin)[\"deploymentId\"])'`"
      - "aws deploy wait deployment-successful --deployment-id $DEP_ID"
      - "aws sqs send-message --queue-url ${aws_sqs_queue.prod_pipeline_notification_queue.url} --message-body \"{\\\"completed_deploy\\\": \\\"${each.key}\\\"}\""
    EOF
  }
}

resource "aws_codestarnotifications_notification_rule" "prod_lambda_pipeline_notification_rule" {
  for_each    = var.lambdas
  detail_type = "FULL"
  // https://docs.aws.amazon.com/dtconsole/latest/userguide/concepts.html#events-ref-buildproject
  event_type_ids = ["codepipeline-pipeline-stage-execution-failed"]

  name     = "prod-pipeline-notification-${each.key}"
  resource = aws_codepipeline.prod_lambda_deploy_pipeline[each.key].arn

  target {
    address = aws_sns_topic.prod_pipeline_notifications.arn
  }
}


data "local_file" "lambda_appspec" {
  filename = "${path.module}/lambda_appspec.yaml"
}

resource "aws_codedeploy_app" "prod_lambda_meta_app" {
  compute_platform = "Lambda"
  name             = "prod-lambda-meta-app"
}


resource "aws_codedeploy_deployment_config" "prod_lambda_meta_config" {
  deployment_config_name = "prod-lambda-meta-config"
  compute_platform       = "Lambda"

  traffic_routing_config {
    type = "AllAtOnce"
  }
}


resource "aws_iam_role" "prod_lambda_deploy_role" {
  name               = "prod-lambda-deploy-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "codedeploy.amazonaws.com"
      },
      "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "prod_lambda_deploy_pipeline_attach" {
  role       = aws_iam_role.prod_lambda_deploy_pipeline_role.name
  policy_arn = aws_iam_policy.prod_lambda_deploy_pipeline_policy.arn
}

resource "aws_iam_role_policy_attachment" "prod_lambda_deploy_attach_default" {
  role       = aws_iam_role.prod_lambda_deploy_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSCodeDeployRoleForLambda"
}

resource "aws_iam_role_policy_attachment" "prod_lambda_deploy_attach_pipeline" {
  role       = aws_iam_role.prod_lambda_deploy_role.name
  policy_arn = aws_iam_policy.prod_lambda_deploy_pipeline_policy.arn
}


resource "aws_codedeploy_deployment_group" "prod_lambda_meta_group" {
  app_name               = aws_codedeploy_app.prod_lambda_meta_app.name
  deployment_group_name  = "prod-lambda-meta-group"
  service_role_arn       = aws_iam_role.prod_lambda_deploy_role.arn
  deployment_config_name = aws_codedeploy_deployment_config.prod_lambda_meta_config.id

  auto_rollback_configuration {
    enabled = true
    events  = ["DEPLOYMENT_FAILURE"] // todo stop on alarm
  }

  deployment_style {
    deployment_option = "WITH_TRAFFIC_CONTROL"
    deployment_type   = "BLUE_GREEN"
  }
}


resource "aws_lambda_function" "prod_pipeline_notifications_slack_lambda" {
  function_name                  = "prod-pipeline-notifications-slack-lambda"
  role                           = aws_iam_role.prod_meta_iam_for_lambda.arn
  handler                        = "build_notification.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = aws_s3_bucket.prod_terraform_pipeline_artifact_store.bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  reserved_concurrent_executions = 1

  environment {
    variables = {
      SLACK_WEBHOOK = "*********************************************************************************"
    }
  }

  depends_on = [
    aws_iam_role_policy_attachment.prod_meta_lambda_logs
  ]
}

resource "aws_lambda_alias" "prod_pipeline_notifications_slack_lambda_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_pipeline_notifications_slack_lambda.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}

resource "aws_lambda_event_source_mapping" "prod_pipeline_notifications_slack_lambda_mapping" {
  event_source_arn = aws_sqs_queue.prod_pipeline_notification_queue.arn
  function_name    = aws_lambda_function.prod_pipeline_notifications_slack_lambda.arn
}
