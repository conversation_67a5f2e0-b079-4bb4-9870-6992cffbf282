
resource "aws_sns_topic" "prod_pipeline_notifications" {
  name = "prod-pipeline-notifications"
}


resource "aws_sns_topic_subscription" "prod_pipeline_notification_subscription" {
  topic_arn = aws_sns_topic.prod_pipeline_notifications.arn
  protocol  = "sqs"
  endpoint  = aws_sqs_queue.prod_pipeline_notification_queue.arn
}

data "aws_iam_policy_document" "prod_pipeline_notifications_sns_access" {
  statement {
    actions = ["sns:Publish"]

    principals {
      type        = "Service"
      identifiers = ["codestar-notifications.amazonaws.com"]
    }

    resources = [aws_sns_topic.prod_pipeline_notifications.arn]
  }
}

resource "aws_sns_topic_policy" "default" {
  arn    = aws_sns_topic.prod_pipeline_notifications.arn
  policy = data.aws_iam_policy_document.prod_pipeline_notifications_sns_access.json
}

resource "aws_codestarnotifications_notification_rule" "prod_pipeline_notification_rule" {
  detail_type = "FULL"
  // https://docs.aws.amazon.com/dtconsole/latest/userguide/concepts.html#events-ref-buildproject
  event_type_ids = ["codepipeline-pipeline-stage-execution-failed"]

  name     = "prod-pipeline-notification"
  resource = aws_codepipeline.prod_terraform_pipeline.arn

  target {
    address = aws_sns_topic.prod_pipeline_notifications.arn
  }
}

resource "aws_sqs_queue" "prod_pipeline_notification_queue" {
  name = "prod-pipeline-notification-queue"
}

resource "aws_sqs_queue_policy" "prod_ipeline_notification_queue_policy" {
  queue_url = aws_sqs_queue.prod_pipeline_notification_queue.id

  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Id": "sqspolicy",
  "Statement": [
    {
      "Sid": "First",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "sqs:SendMessage",
      "Resource": "${aws_sqs_queue.prod_pipeline_notification_queue.arn}",
      "Condition": {
        "ArnEquals": {
          "aws:SourceArn": "${aws_sns_topic.prod_pipeline_notifications.arn}"
        }
      }
    }
  ]
}
POLICY
}



resource "aws_sns_topic" "prod_cloudwatch_alarms" {
  name = "prod-cloudwatch-alarms"
}

resource "aws_sns_topic_subscription" "prod_cloudwatch_alarms_subscriptions" {
  topic_arn = aws_sns_topic.prod_cloudwatch_alarms.arn
  protocol  = "email"
  endpoint  = each.key
  for_each  = toset(["<EMAIL>", "<EMAIL>"])
}
