data "aws_iam_policy_document" "prod_plan_change_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_plan_change_policy" {
  name        = "prod-plan-change-policy"
  path        = "/"
  description = "IAM policy for plan change"
  policy      = data.aws_iam_policy_document.prod_plan_change_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_plan_change_attach" {
  role       = aws_iam_role.prod_plan_change_role.name
  policy_arn = aws_iam_policy.prod_plan_change_policy.arn
}

resource "aws_iam_role" "prod_plan_change_role" {
  name = "prod_plan_change_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_plan_change" {
  function_name                  = "prod-plan-change"
  role                           = aws_iam_role.prod_plan_change_role.arn
  handler                        = "lambda.plan_change.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 180
  reserved_concurrent_executions = 1

  memory_size = 128

  environment {
    variables = {
      DJANGO_SETTINGS_MODULE = "nexus.lambda_settings"
      DB_USER                = local.nexus_creds["DB_USER"]
      DB_PASSWORD            = local.nexus_creds["DB_PASSWORD"]
      DB_HOST                = aws_db_instance.prod_nexus.address
      DB_NAME                = aws_db_instance.prod_nexus.db_name
      CDR_DB_API_TOKEN       = local.cdr_db_creds["NEXUS_API_TOKEN"] # Not a bug, it's the nexus token for cdr, needs renaming
      LAMBDA_API_TOKEN       = local.nexus_creds["LAMBDA_API_TOKEN"]
    }
  }
}

resource "aws_lambda_alias" "prod_plan_change_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_plan_change.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}
