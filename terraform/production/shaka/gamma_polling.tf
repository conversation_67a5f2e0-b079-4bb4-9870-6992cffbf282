data "aws_iam_policy_document" "prod_fetch_gamma_sftp_files_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }
  statement {
    effect    = "Allow"
    actions   = ["s3:*"]
    resources = ["${aws_s3_bucket.prod_gamma_cdr_files.arn}", "${aws_s3_bucket.prod_gamma_cdr_files.arn}/*", "${aws_s3_bucket.prod_gamma_cdr_files.arn}/"]
  }

}

resource "aws_iam_policy" "prod_fetch_gamma_sftp_files_policy" {
  name        = "prod-fetch-gamma-sftp-files-policy"
  path        = "/"
  description = "IAM policy for the gamma sftp fetch"
  policy      = data.aws_iam_policy_document.prod_fetch_gamma_sftp_files_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_fetch_gamma_sftp_files_attach" {
  role       = aws_iam_role.prod_fetch_gamma_sftp_files_role.name
  policy_arn = aws_iam_policy.prod_fetch_gamma_sftp_files_policy.arn
}

resource "aws_iam_role" "prod_fetch_gamma_sftp_files_role" {
  name = "prod_fetch_gamma_sftp_files_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_fetch_gamma_sftp_files" {
  function_name                  = "prod-fetch-gamma-sftp-files"
  role                           = aws_iam_role.prod_fetch_gamma_sftp_files_role.arn
  handler                        = "fetch_gamma_sftp_files.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 90
  reserved_concurrent_executions = 2

  environment {
    variables = {
      S3_BUCKET_NAME = aws_s3_bucket.prod_gamma_cdr_files.bucket
      SFTP_USERNAME  = local.gamma_sftp_creds["SFTP_USERNAME"]
      SFTP_PKEY_KEY  = local.gamma_sftp_creds["SFTP_PKEY_KEY"]
      SFTP_HOST      = "www.gamma-portal.com"
      SFTP_PORT      = 22
      SFTP_DIR       = "/Cdrs"
    }
  }
}

resource "aws_lambda_alias" "prod_fetch_gamma_sftp_files_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_fetch_gamma_sftp_files.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}

resource "aws_iam_role" "prod_fetch_gamma_sftp_files_schedule_role" {
  name = "prod_fetch_gamma_sftp_files_schedule_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

data "aws_iam_policy_document" "prod_fetch_gamma_sftp_files_schedule_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_fetch_gamma_sftp_files_schedule_policy" {
  name        = "prod-fetch-gamma-sftp-files-schedule-policy"
  path        = "/"
  description = "IAM policy for the gamma sftp fetch scheduler"
  policy      = data.aws_iam_policy_document.prod_fetch_gamma_sftp_files_schedule_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_fetch_gamma_sftp_files_scedule_attach" {
  role       = aws_iam_role.prod_fetch_gamma_sftp_files_schedule_role.name
  policy_arn = aws_iam_policy.prod_fetch_gamma_sftp_files_schedule_policy.arn
}


resource "aws_scheduler_schedule" "prod_fetch_gamma_sftp_files_schedule" {
  name       = "prod-fetch-gamma-sftp-files-schedule"
  group_name = "default"
  flexible_time_window {
    mode = "OFF"
  }
  description         = "Fires every 5 minutes to fetch gamma files from sftp"
  schedule_expression = "rate(5 minutes)"
  target {
    arn      = aws_lambda_function.prod_fetch_gamma_sftp_files.arn
    role_arn = aws_iam_role.prod_fetch_gamma_sftp_files_schedule_role.arn
    retry_policy {
      maximum_retry_attempts = 0
    }
  }
}


resource "aws_s3_bucket" "prod_gamma_cdr_files" {
  bucket = "prod-gamma-cdr-files"
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_s3_bucket_versioning" "prod_gamma_cdr_files_versioning" {
  bucket = aws_s3_bucket.prod_gamma_cdr_files.id
  versioning_configuration {
    status = "Enabled"
  }
}




data "aws_iam_policy_document" "prod_gamma_cdr_files_notification_policy" {
  statement {
    effect = "Allow"

    principals {
      type        = "*"
      identifiers = ["*"]
    }

    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:*:*:*"]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values   = [aws_s3_bucket.prod_gamma_cdr_files.arn]
    }
  }
}

resource "aws_sqs_queue" "prod_gamma_new_file_queue" {
  name                       = "prod-gamma-new-file-queue"
  policy                     = data.aws_iam_policy_document.prod_gamma_cdr_files_notification_policy.json
  visibility_timeout_seconds = 90
}

resource "aws_s3_bucket_notification" "prod_gamma_cdr_files_notification" {
  bucket = aws_s3_bucket.prod_gamma_cdr_files.id

  queue {
    queue_arn = aws_sqs_queue.prod_gamma_new_file_queue.arn
    events    = ["s3:ObjectCreated:*"]
  }
}



/* Django processing files */
data "aws_iam_policy_document" "prod_import_gamma_cdr_file_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }
  statement {
    effect    = "Allow"
    actions   = ["s3:*"] /* todo: readonly */
    resources = ["${aws_s3_bucket.prod_gamma_cdr_files.arn}", "${aws_s3_bucket.prod_gamma_cdr_files.arn}/*", "${aws_s3_bucket.prod_gamma_cdr_files.arn}/"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["sqs:*"] /* todo: readonly */
    resources = ["${aws_sqs_queue.prod_gamma_new_file_queue.arn}"]
  }

}

resource "aws_iam_policy" "prod_import_gamma_cdr_file_policy" {
  name        = "prod-import-gamma-cdr-file-policy"
  path        = "/"
  description = "IAM policy for the cdr file import"
  policy      = data.aws_iam_policy_document.prod_import_gamma_cdr_file_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_import_gamma_cdr_file_attach" {
  role       = aws_iam_role.prod_import_gamma_cdr_file_role.name
  policy_arn = aws_iam_policy.prod_import_gamma_cdr_file_policy.arn
}

resource "aws_iam_role" "prod_import_gamma_cdr_file_role" {
  name = "prod_import_gamma_cdr_file_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_import_gamma_cdr_file" {
  function_name                  = "prod-import-gamma-cdr-file"
  role                           = aws_iam_role.prod_import_gamma_cdr_file_role.arn
  handler                        = "lambda.import_gamma_cdr_file.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 90
  reserved_concurrent_executions = 2

  memory_size = 256

  vpc_config {
    subnet_ids         = ["subnet-043ded321d0fab1dd", "subnet-01190ee0916d2651a", "subnet-0bf717b067734bbac"]
    security_group_ids = ["sg-034b2cf978c016575"]
  }


  environment {
    variables = {
      S3_BUCKET_NAME         = aws_s3_bucket.prod_gamma_cdr_files.bucket
      DJANGO_SETTINGS_MODULE = "cdr_db.lambda_settings"
      DB_USER                = local.cdr_db_creds["DB_USER"]
      DB_PASSWORD            = local.cdr_db_creds["DB_PASSWORD"]
      DB_HOST                = aws_db_instance.prod_cdr_db.address
      DB_NAME                = aws_db_instance.prod_cdr_db.db_name
    }
  }
}

resource "aws_lambda_alias" "prod_import_gamma_cdr_file_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_import_gamma_cdr_file.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}


resource "aws_lambda_event_source_mapping" "prod_import_gamma_cdr_file_mapping" {
  event_source_arn = aws_sqs_queue.prod_gamma_new_file_queue.arn
  function_name    = aws_lambda_function.prod_import_gamma_cdr_file.arn
}
