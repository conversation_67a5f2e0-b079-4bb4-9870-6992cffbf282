To run terraform locally:

install terraform
cd to the appropriate terraform directory e.g. terraform/production/meta for meta prod stuff
terraform init - this should pick up on the backend. If you don't have AWS access you'll need access keys or a role. For example, set up a config in ~/.aws/config like

[profile shaka]
aws_access_key_id = <key>
aws_secret_access_key = <secret>

and then do export AWS_PROFILE=shaka

before running the terraform init again. Once it's happy you should be able to terraform plan and terraform apply.


Currently most things aren't managed by terraform. Eventually only the shaka-terraform-state-container bucket and shaka-terraform-state-locking table in dynamodb and kms key will be non-terraform-managed.

Security boundaries: in effect the terraform role needs full access to everything, and by extension so does the code pipeline that runs it. It's not ideal but it's close enough. It would be nice to isolate the resources by prod/dev and to limit access very specifically but it's probably not worth the effort.

The code-pipeline-terraform-apply was mostly taken from https://www.chrisfarris.com/post/tf-codepipeline/ and https://github.com/aws-samples/aws-codepipeline-terraform-cicd-samples/

The code pipeline stages are roughly

source -> plan meta -> apply meta -> plan infra -> apply infra -> code deploy


Handling permission errors: if you get an error in the logs like 'user ... is not authorized to perform ....' in the terraform apply stages then it's probably because the policy for the codepipeline role doesn't have permission on that resource, so add it to the pipeline policy. You will probably have to apply terraform manually as the apply will still fail so it won't get the new permission.


To deploy a new lambda, add the code to the project, and add the lambda to undeployed_lambda_names and set the path in main.tf. Then run a full pipeline - this will build the artifact. Afterwards you can add the function itself and create the entry in var lambdas and remove the undeployed bit. Finally you can add the alias because it requires an existing version.

Lambda names must be unqiue and have no spaces or special characters because I put them in file paths without quoting

The alias should really have ignore_changes set for the function version but updating the alias does not cause a deploy. The version that's deployed stays deployed until you 'publish' a new version. At that point we set the alias to the latest version and publish the alias.




Upadting the cloudwatch dashboard consists of manually doing it and then copying the json out.

Encrypting plaintext creds: aws kms encrypt --key-id e73d0e08-6282-47b4-a049-5648fb584809 --plaintext fileb://./shaka/transatel_sftp.yml.plaintext  --output text --query CiphertextBlob > ./shaka/transatel_sftp.yml.encrypted

Decrpyting to update/add: aws kms decrypt --key-id e73d0e08-6282-47b4-a049-5648fb584809 --ciphertext-blob `cat shaka/transatel_sftp.yml.encrypted`  --output text --query Plaintext | base64 --decode > ./shaka/transatel_sftp.yml.plaintext

Not sure why the --ciphertext-bob fileb:// didn't work.

