# Next.js Monorepo Structure Plan

## Overview

This document outlines the structure and configuration for a Next.js monorepo setup that allows:
- Shared code between multiple Next.js applications
- Single package management with one node_modules directory
- Centralized configuration and build scripts
- Individual app development and deployment

## Directory Structure

```
next-subscriber-app/
├── package.json             # Root package.json for managing all apps
├── next.config.js           # Shared Next.js configuration
├── tsconfig.json            # Base TypeScript configuration
├── .env                     # Global environment variables
├── .env.local               # Local environment overrides (gitignored)
├── node_modules/            # Single node_modules directory for all apps
├── src/
│   ├── api/                 # Shared API utilities
│   ├── components/          # Shared UI components
│   ├── hooks/               # Shared React hooks
│   ├── types/               # Shared TypeScript types
│   ├── utils/               # Shared utility functions
│   ├── uswitch/             # uswitch app
│   │   ├── app/             # Next.js App Router structure
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx
│   │   │   └── ...
│   │   ├── public/          # App-specific static files
│   │   └── .env.local       # App-specific environment variables
│   └── [other-app]/         # Other Next.js apps following same structure
└── scripts/                 # Build and utility scripts
```

## Configuration Files

### Root package.json

The root package.json will:
- Declare all dependencies used across apps
- Define workspace configuration
- Provide scripts to run, build, and test individual apps

### next.config.js

The shared Next.js configuration will:
- Use `transpilePackages` to enable code sharing between apps
- Configure path aliases for easy imports
- Allow app-specific overrides when needed

### tsconfig.json

The base TypeScript configuration will:
- Set up path aliases matching the directory structure
- Configure shared compiler options
- Allow app-specific extensions

## Running Individual Apps

Scripts in the root package.json will allow:
- `npm run dev:uswitch` - Run the uswitch app in development mode
- `npm run build:uswitch` - Build the uswitch app for production
- `npm run start:uswitch` - Start the uswitch app in production mode

## Environment Variables

Environment variables will be managed through:
- Root-level `.env` files for shared variables
- App-specific `.env.local` files for overrides
- Build-time environment variable injection

## Code Sharing Strategy

Code sharing will be implemented through:
- Direct imports from shared directories
- Path aliases for clean import statements
- TypeScript for type safety across the monorepo
- Next.js `transpilePackages` for proper bundling

## Implementation Steps

1. Initialize the monorepo structure
2. Configure the root package.json
3. Set up the shared Next.js configuration
4. Configure TypeScript for the monorepo
5. Create the directory structure
6. Set up the first app (uswitch)
7. Configure build and run scripts
8. Test the setup with shared components

## Deployment Considerations

- Each app can be built and deployed independently
- Shared code will be bundled with each app at build time
- Environment variables can control build-specific behavior
